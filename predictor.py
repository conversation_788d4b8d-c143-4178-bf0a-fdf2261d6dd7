"""
Model loading and video prediction module
"""

import os
import numpy as np
import logging
from pathlib import Path
from config import PREDICTION_CONFIG, MODELS_PATH, DATASET_CONFIG, get_model_path
from extractor import get_feature_extractor

logger = logging.getLogger(__name__)

class DeepfakePredictor:
    """Handles loading models and making predictions on videos"""
    
    def __init__(self, model_name="best_random_forest"):
        self.model_name = model_name
        self.model = None
        self.scaler = None
        self.pca = None
        self.feature_extractor = None
        
        logger.info(f"Initializing DeepfakePredictor with model: {model_name}")
        
        # Load model components
        self._load_model_components()
        
        # Initialize feature extractor
        self.feature_extractor = get_feature_extractor()
    
    def _load_model_components(self):
        """Load model, scaler, and PCA components"""
        try:
            import joblib
            
            # Load main model
            model_path = get_model_path(self.model_name)
            if os.path.exists(model_path):
                self.model = joblib.load(model_path)
                logger.info(f"Loaded model: {model_path}")
            else:
                # Try fallback models
                fallback_models = ["random_forest", "best_random_forest", "svm", "xgboost"]
                for fallback in fallback_models:
                    fallback_path = get_model_path(fallback)
                    if os.path.exists(fallback_path):
                        self.model = joblib.load(fallback_path)
                        self.model_name = fallback
                        logger.info(f"Loaded fallback model: {fallback_path}")
                        break
                
                if self.model is None:
                    raise FileNotFoundError(f"No trained model found in {MODELS_PATH}")
            
            # Load scaler
            scaler_path = get_model_path(f"{self.model_name}_scaler")
            if os.path.exists(scaler_path):
                self.scaler = joblib.load(scaler_path)
                logger.info(f"Loaded scaler: {scaler_path}")
            
            # Load PCA
            pca_path = get_model_path(f"{self.model_name}_pca")
            if os.path.exists(pca_path):
                self.pca = joblib.load(pca_path)
                logger.info(f"Loaded PCA: {pca_path}")
            
        except ImportError:
            logger.error("joblib not available - cannot load models")
            raise
        except Exception as e:
            logger.error(f"Error loading model components: {e}")
            raise
    
    def preprocess_features(self, features):
        """Apply preprocessing (scaling, PCA) to features"""
        if features.size == 0:
            return features
        
        # Reshape for single sample
        features = features.reshape(1, -1)
        
        # Apply scaling
        if self.scaler is not None:
            features = self.scaler.transform(features)
            logger.debug("Applied feature scaling")
        
        # Apply PCA
        if self.pca is not None:
            features = self.pca.transform(features)
            logger.debug("Applied PCA transformation")
        
        return features
    
    def predict_video(self, video_path):
        """Predict if a video is real or fake"""
        logger.info(f"Analyzing video: {os.path.basename(video_path)}")
        
        # Check if video exists
        if not os.path.exists(video_path):
            logger.error(f"Video file not found: {video_path}")
            return None
        
        # Check if model is loaded
        if self.model is None:
            logger.error("No model loaded")
            return None
        
        try:
            # Extract features
            logger.debug("Extracting features...")
            features = self.feature_extractor.extract_features_from_video(video_path)
            
            if features.size == 0:
                logger.warning("No features extracted from video")
                return {
                    "prediction": "unknown",
                    "confidence": 0.0,
                    "probabilities": {"real": 0.5, "fake": 0.5},
                    "status": "failed",
                    "error": "No features extracted"
                }
            
            # Preprocess features
            processed_features = self.preprocess_features(features)
            
            # Make prediction
            prediction = self.model.predict(processed_features)[0]
            
            # Get probabilities if available
            probabilities = {"real": 0.5, "fake": 0.5}
            if hasattr(self.model, 'predict_proba'):
                proba = self.model.predict_proba(processed_features)[0]
                probabilities = {"real": float(proba[0]), "fake": float(proba[1])}
            
            # Determine result
            result = "fake" if prediction == 1 else "real"
            confidence = max(probabilities.values())
            
            # Check confidence threshold
            if confidence < PREDICTION_CONFIG["confidence_threshold"]:
                result = "uncertain"
            
            logger.info(f"Prediction: {result} (confidence: {confidence:.4f})")
            
            return {
                "prediction": result,
                "confidence": confidence,
                "probabilities": probabilities,
                "status": "success",
                "model_used": self.model_name,
                "features_extracted": len(features)
            }
            
        except Exception as e:
            logger.error(f"Error predicting video {video_path}: {e}")
            return {
                "prediction": "unknown",
                "confidence": 0.0,
                "probabilities": {"real": 0.5, "fake": 0.5},
                "status": "error",
                "error": str(e)
            }
    
    def predict_batch(self, video_directory):
        """Predict multiple videos in a directory"""
        logger.info(f"Starting batch prediction for directory: {video_directory}")
        
        if not os.path.exists(video_directory):
            logger.error(f"Directory not found: {video_directory}")
            return []
        
        results = []
        video_files = []
        
        # Find video files
        for filename in os.listdir(video_directory):
            if any(filename.lower().endswith(ext) for ext in DATASET_CONFIG["supported_formats"]):
                video_files.append(os.path.join(video_directory, filename))
        
        if not video_files:
            logger.warning("No video files found in directory")
            return results
        
        logger.info(f"Found {len(video_files)} videos to analyze")
        
        # Process each video
        for i, video_path in enumerate(video_files, 1):
            logger.info(f"Processing video {i}/{len(video_files)}: {os.path.basename(video_path)}")
            
            result = self.predict_video(video_path)
            result["filename"] = os.path.basename(video_path)
            result["video_path"] = video_path
            results.append(result)
        
        # Summary statistics
        successful = [r for r in results if r["status"] == "success"]
        real_count = len([r for r in successful if r["prediction"] == "real"])
        fake_count = len([r for r in successful if r["prediction"] == "fake"])
        uncertain_count = len([r for r in successful if r["prediction"] == "uncertain"])
        
        logger.info(f"Batch prediction completed:")
        logger.info(f"  Total videos: {len(results)}")
        logger.info(f"  Successful: {len(successful)}")
        logger.info(f"  Real: {real_count}")
        logger.info(f"  Fake: {fake_count}")
        logger.info(f"  Uncertain: {uncertain_count}")
        
        return results

class EnsemblePredictor:
    """Ensemble predictor that combines multiple models"""
    
    def __init__(self, model_names=None):
        if model_names is None:
            model_names = ["random_forest", "svm", "xgboost"]
        
        self.predictors = {}
        self.model_names = []
        
        # Load available models
        for model_name in model_names:
            try:
                predictor = DeepfakePredictor(model_name)
                if predictor.model is not None:
                    self.predictors[model_name] = predictor
                    self.model_names.append(model_name)
                    logger.info(f"Loaded ensemble model: {model_name}")
            except Exception as e:
                logger.warning(f"Failed to load model {model_name}: {e}")
        
        if not self.predictors:
            raise ValueError("No models available for ensemble")
        
        logger.info(f"Initialized ensemble with {len(self.predictors)} models")
    
    def predict_video(self, video_path):
        """Predict using ensemble of models"""
        logger.info(f"Ensemble prediction for: {os.path.basename(video_path)}")
        
        predictions = []
        probabilities = []
        individual_results = {}
        
        # Get predictions from all models
        for model_name, predictor in self.predictors.items():
            result = predictor.predict_video(video_path)
            
            if result["status"] == "success":
                predictions.append(1 if result["prediction"] == "fake" else 0)
                probabilities.append(result["probabilities"]["fake"])
                individual_results[model_name] = result
        
        if not predictions:
            logger.error("No successful predictions from ensemble models")
            return {
                "prediction": "unknown",
                "confidence": 0.0,
                "probabilities": {"real": 0.5, "fake": 0.5},
                "status": "failed",
                "error": "All ensemble models failed"
            }
        
        # Combine predictions
        if PREDICTION_CONFIG["ensemble_voting"] == "hard":
            # Majority voting
            final_prediction = 1 if sum(predictions) > len(predictions) / 2 else 0
            confidence = sum(predictions) / len(predictions)
        else:
            # Soft voting (average probabilities)
            avg_fake_prob = np.mean(probabilities)
            final_prediction = 1 if avg_fake_prob > 0.5 else 0
            confidence = max(avg_fake_prob, 1 - avg_fake_prob)
        
        result = "fake" if final_prediction == 1 else "real"
        
        logger.info(f"Ensemble prediction: {result} (confidence: {confidence:.4f})")
        
        return {
            "prediction": result,
            "confidence": confidence,
            "probabilities": {"real": 1 - np.mean(probabilities), "fake": np.mean(probabilities)},
            "status": "success",
            "ensemble_size": len(predictions),
            "individual_results": individual_results
        }

def predict_video(video_path, model_name="best_random_forest", use_ensemble=False):
    """Convenience function for single video prediction"""
    if use_ensemble:
        predictor = EnsemblePredictor()
    else:
        predictor = DeepfakePredictor(model_name)
    
    return predictor.predict_video(video_path)

def predict_batch(video_directory, model_name="best_random_forest", use_ensemble=False):
    """Convenience function for batch prediction"""
    if use_ensemble:
        predictor = EnsemblePredictor()
    else:
        predictor = DeepfakePredictor(model_name)
    
    return predictor.predict_batch(video_directory)

if __name__ == "__main__":
    # Test prediction
    from config import initialize_system
    
    if initialize_system():
        try:
            predictor = DeepfakePredictor()
            print("✅ Predictor initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize predictor: {e}")
    else:
        print("❌ Failed to initialize system")

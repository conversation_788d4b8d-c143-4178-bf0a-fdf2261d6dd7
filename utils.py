"""
Utility functions for enhancement, normalization, smoothing, and logging
"""

import os
import numpy as np
import logging
from pathlib import Path
from config import SYSTEM_CONFIG, DATASET_CONFIG

logger = logging.getLogger(__name__)

class VideoEnhancer:
    """Video quality enhancement utilities"""
    
    def __init__(self):
        self.enhancement_enabled = True
        logger.debug("Initialized VideoEnhancer")
    
    def enhance_frame(self, frame):
        """Apply comprehensive frame enhancement"""
        if not self.enhancement_enabled:
            return frame
        
        try:
            import cv2
            
            # Apply enhancement pipeline
            enhanced = frame.copy()
            
            # 1. Denoising
            enhanced = self.denoise_frame(enhanced)
            
            # 2. Sharpening
            enhanced = self.sharpen_frame(enhanced)
            
            # 3. Contrast enhancement
            enhanced = self.enhance_contrast(enhanced)
            
            # 4. Brightness adjustment
            enhanced = self.adjust_brightness(enhanced)
            
            return enhanced
            
        except Exception as e:
            logger.warning(f"Frame enhancement failed: {e}")
            return frame
    
    def denoise_frame(self, frame):
        """Remove noise from frame"""
        try:
            import cv2
            return cv2.fastNlMeansDenoisingColored(frame, None, 10, 10, 7, 21)
        except:
            return frame
    
    def sharpen_frame(self, frame):
        """Apply sharpening filter"""
        try:
            import cv2
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            return cv2.filter2D(frame, -1, kernel)
        except:
            return frame
    
    def enhance_contrast(self, frame):
        """Enhance contrast using CLAHE"""
        try:
            import cv2
            lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            l = clahe.apply(l)
            enhanced = cv2.merge([l, a, b])
            return cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        except:
            return frame
    
    def adjust_brightness(self, frame, factor=1.1):
        """Adjust frame brightness"""
        try:
            return np.clip(frame * factor, 0, 255).astype(np.uint8)
        except:
            return frame
    
    def resize_frame(self, frame, target_size):
        """Resize frame with quality preservation"""
        try:
            import cv2
            height, width = frame.shape[:2]
            
            if height < target_size or width < target_size:
                # Upscale with cubic interpolation
                return cv2.resize(frame, (target_size, target_size), interpolation=cv2.INTER_CUBIC)
            else:
                # Downscale with area interpolation
                return cv2.resize(frame, (target_size, target_size), interpolation=cv2.INTER_AREA)
        except:
            return frame

class FeatureNormalizer:
    """Feature normalization and preprocessing utilities"""
    
    def __init__(self):
        logger.debug("Initialized FeatureNormalizer")
    
    def normalize_features(self, features):
        """Normalize feature vector"""
        if features.size == 0:
            return features
        
        # L2 normalization
        norm = np.linalg.norm(features)
        if norm > 0:
            return features / norm
        return features
    
    def standardize_features(self, features):
        """Standardize features (zero mean, unit variance)"""
        if features.size == 0:
            return features
        
        mean = np.mean(features)
        std = np.std(features)
        
        if std > 0:
            return (features - mean) / std
        return features - mean
    
    def robust_scale_features(self, features):
        """Robust scaling using median and IQR"""
        if features.size == 0:
            return features
        
        median = np.median(features)
        q75, q25 = np.percentile(features, [75, 25])
        iqr = q75 - q25
        
        if iqr > 0:
            return (features - median) / iqr
        return features - median
    
    def clip_outliers(self, features, percentile=95):
        """Clip extreme outliers"""
        if features.size == 0:
            return features
        
        lower = np.percentile(features, 100 - percentile)
        upper = np.percentile(features, percentile)
        
        return np.clip(features, lower, upper)

class FeatureSmoother:
    """Feature smoothing and temporal consistency utilities"""
    
    def __init__(self):
        logger.debug("Initialized FeatureSmoother")
    
    def smooth_temporal_features(self, feature_sequence):
        """Apply temporal smoothing to feature sequence"""
        if len(feature_sequence) < 3:
            return feature_sequence
        
        try:
            # Apply Gaussian smoothing
            from scipy.ndimage import gaussian_filter1d
            smoothed = []
            
            for i in range(len(feature_sequence[0])):
                feature_channel = [features[i] for features in feature_sequence]
                smoothed_channel = gaussian_filter1d(feature_channel, sigma=1.0)
                smoothed.append(smoothed_channel)
            
            # Reconstruct feature sequence
            result = []
            for i in range(len(feature_sequence)):
                result.append(np.array([smoothed[j][i] for j in range(len(smoothed))]))
            
            return result
            
        except ImportError:
            logger.warning("SciPy not available for temporal smoothing")
            return feature_sequence
        except Exception as e:
            logger.warning(f"Temporal smoothing failed: {e}")
            return feature_sequence
    
    def apply_moving_average(self, features, window_size=3):
        """Apply moving average smoothing"""
        if len(features) < window_size:
            return features
        
        smoothed = []
        for i in range(len(features)):
            start = max(0, i - window_size // 2)
            end = min(len(features), i + window_size // 2 + 1)
            window = features[start:end]
            smoothed.append(np.mean(window, axis=0))
        
        return smoothed

class DatasetValidator:
    """Dataset validation and quality checking utilities"""
    
    def __init__(self):
        logger.debug("Initialized DatasetValidator")
    
    def validate_video_file(self, video_path):
        """Validate a single video file"""
        if not os.path.exists(video_path):
            return False, "File does not exist"
        
        # Check file extension
        if not any(video_path.lower().endswith(ext) for ext in DATASET_CONFIG["supported_formats"]):
            return False, "Unsupported video format"
        
        # Check file size
        file_size = os.path.getsize(video_path)
        if file_size < 1024:  # Less than 1KB
            return False, "File too small"
        
        # Try to open with OpenCV
        try:
            import cv2
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return False, "Cannot open video file"
            
            # Check video properties
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            
            if frame_count == 0:
                cap.release()
                return False, "No frames in video"
            
            if fps <= 0:
                cap.release()
                return False, "Invalid frame rate"
            
            duration = frame_count / fps
            if duration < DATASET_CONFIG["min_video_duration"]:
                cap.release()
                return False, f"Video too short ({duration:.1f}s)"
            
            if duration > DATASET_CONFIG["max_video_duration"]:
                cap.release()
                return False, f"Video too long ({duration:.1f}s)"
            
            cap.release()
            return True, "Valid video file"
            
        except ImportError:
            # If OpenCV not available, just check basic properties
            return True, "Basic validation passed (OpenCV not available)"
        except Exception as e:
            return False, f"Validation error: {e}"
    
    def validate_dataset_structure(self, dataset_path):
        """Validate dataset directory structure"""
        issues = []
        
        if not os.path.exists(dataset_path):
            issues.append("Dataset directory does not exist")
            return issues
        
        # Check for real/fake subdirectories
        real_path = os.path.join(dataset_path, "real")
        fake_path = os.path.join(dataset_path, "fake")
        
        if not os.path.exists(real_path):
            issues.append("Missing 'real' subdirectory")
        
        if not os.path.exists(fake_path):
            issues.append("Missing 'fake' subdirectory")
        
        # Count videos in each category
        real_count = 0
        fake_count = 0
        
        if os.path.exists(real_path):
            real_videos = [f for f in os.listdir(real_path) 
                          if any(f.lower().endswith(ext) for ext in DATASET_CONFIG["supported_formats"])]
            real_count = len(real_videos)
        
        if os.path.exists(fake_path):
            fake_videos = [f for f in os.listdir(fake_path) 
                          if any(f.lower().endswith(ext) for ext in DATASET_CONFIG["supported_formats"])]
            fake_count = len(fake_videos)
        
        if real_count == 0:
            issues.append("No real videos found")
        
        if fake_count == 0:
            issues.append("No fake videos found")
        
        if real_count < 2 or fake_count < 2:
            issues.append("Insufficient videos for training (need at least 2 of each class)")
        
        # Check for class imbalance
        if real_count > 0 and fake_count > 0:
            ratio = max(real_count, fake_count) / min(real_count, fake_count)
            if ratio > 10:
                issues.append(f"Severe class imbalance: {real_count} real vs {fake_count} fake")
        
        return issues

class PerformanceMonitor:
    """Performance monitoring and profiling utilities"""
    
    def __init__(self):
        self.timings = {}
        logger.debug("Initialized PerformanceMonitor")
    
    def start_timer(self, operation):
        """Start timing an operation"""
        import time
        self.timings[operation] = time.time()
    
    def end_timer(self, operation):
        """End timing and log duration"""
        import time
        if operation in self.timings:
            duration = time.time() - self.timings[operation]
            logger.info(f"Operation '{operation}' took {duration:.2f} seconds")
            del self.timings[operation]
            return duration
        return None
    
    def monitor_memory_usage(self):
        """Monitor current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            logger.debug(f"Memory usage: {memory_mb:.1f} MB")
            return memory_mb
        except ImportError:
            logger.debug("psutil not available for memory monitoring")
            return None

def setup_project_structure():
    """Setup complete project directory structure"""
    from config import PROJECT_ROOT, DATASET_PATH, FEATURES_PATH, MODELS_PATH
    
    directories = [
        DATASET_PATH,
        os.path.join(DATASET_PATH, "real"),
        os.path.join(DATASET_PATH, "fake"),
        FEATURES_PATH,
        MODELS_PATH,
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.debug(f"Created directory: {directory}")
    
    logger.info("Project structure setup completed")

def get_system_info():
    """Get comprehensive system information"""
    info = {
        "python_version": None,
        "platform": None,
        "cpu_count": None,
        "memory_gb": None,
        "gpu_available": False,
        "opencv_version": None,
        "torch_version": None,
    }
    
    try:
        import sys
        info["python_version"] = sys.version
        
        import platform
        info["platform"] = platform.platform()
        
        import multiprocessing
        info["cpu_count"] = multiprocessing.cpu_count()
        
        import psutil
        info["memory_gb"] = psutil.virtual_memory().total / (1024**3)
    except ImportError:
        pass
    
    try:
        import cv2
        info["opencv_version"] = cv2.__version__
    except ImportError:
        pass
    
    try:
        import torch
        info["torch_version"] = torch.__version__
        info["gpu_available"] = torch.cuda.is_available()
    except ImportError:
        pass
    
    return info

def log_system_info():
    """Log comprehensive system information"""
    info = get_system_info()
    
    logger.info("System Information:")
    for key, value in info.items():
        if value is not None:
            logger.info(f"  {key}: {value}")

if __name__ == "__main__":
    # Test utilities
    from config import initialize_system
    
    if initialize_system():
        log_system_info()
        setup_project_structure()
        print("✅ Utilities tested successfully")
    else:
        print("❌ Failed to initialize system")

#!/usr/bin/env python3
"""
Test the best performing model (SVM)
"""

import os
from config import initialize_system
from predictor import DeepfakePredictor

def test_svm_model():
    """Test the SVM model which performed best in training"""
    print("🎯 TESTING BALANCED MODEL")
    print("="*40)
    
    if not initialize_system():
        print("❌ System initialization failed!")
        return
    
    # Use the balanced model
    try:
        predictor = DeepfakePredictor("balanced_rf")
        print("✅ Loaded balanced Random Forest model successfully")
    except Exception as e:
        print(f"❌ Failed to load balanced model: {e}")
        print("🔄 Falling back to SVM model...")
        try:
            predictor = DeepfakePredictor("best_svm")
            print("✅ Loaded SVM model")
        except Exception as e2:
            print(f"❌ Failed to load any model: {e2}")
            return
    
    # Test videos
    test_videos = [
        ("dataset/real/image 2 (21).mp4", "REAL"),
        ("dataset/fake/image 1.mp4", "FAKE"),
        ("dataset/real/image 2 (4).mp4", "REAL"),
        ("dataset/fake/image 2 (8).mp4", "FAKE"),
    ]
    
    print("\n🧪 TESTING VIDEOS:")
    print("-" * 50)
    
    correct_predictions = 0
    total_predictions = 0
    
    for video_path, expected in test_videos:
        if os.path.exists(video_path):
            print(f"\n📹 Testing: {os.path.basename(video_path)} (Expected: {expected})")
            
            try:
                result = predictor.predict_video(video_path)
                prediction = result['prediction'].upper()
                confidence = result['confidence']
                
                # Check if prediction is correct
                is_correct = prediction == expected
                if is_correct:
                    correct_predictions += 1
                    status = "✅ CORRECT"
                else:
                    status = "❌ WRONG"
                
                total_predictions += 1
                
                print(f"   Result: {prediction} ({confidence:.3f}) {status}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        else:
            print(f"❌ Video not found: {video_path}")
    
    # Calculate accuracy
    if total_predictions > 0:
        accuracy = (correct_predictions / total_predictions) * 100
        print(f"\n📊 OVERALL RESULTS:")
        print(f"   Correct: {correct_predictions}/{total_predictions}")
        print(f"   Accuracy: {accuracy:.1f}%")
        
        if accuracy >= 75:
            print("🎉 Good performance!")
        elif accuracy >= 50:
            print("⚠️  Moderate performance - needs improvement")
        else:
            print("❌ Poor performance - major issues")
    
    return correct_predictions, total_predictions

def suggest_next_steps():
    """Suggest next steps based on results"""
    print("\n💡 NEXT STEPS:")
    print("="*30)
    print("1. 📹 Check your video quality:")
    print("   - Are the 'fake' videos actually detectable deepfakes?")
    print("   - Do they have clear faces and good resolution?")
    
    print("\n2. 🔍 Verify training data:")
    print("   - Make sure real videos are genuine")
    print("   - Ensure fake videos are obvious deepfakes")
    
    print("\n3. 📊 Add more diverse data:")
    print("   - Get 20+ real videos from different sources")
    print("   - Get 20+ fake videos with different deepfake methods")
    
    print("\n4. ⚙️  Try different approaches:")
    print("   - Use ensemble prediction")
    print("   - Adjust confidence thresholds")
    print("   - Try different feature extraction settings")

if __name__ == "__main__":
    correct, total = test_svm_model()
    suggest_next_steps()
    
    print(f"\n🎯 SUMMARY: {correct}/{total} correct predictions")

# Deepfake Detection System Requirements

# Core dependencies (required)
numpy>=1.21.0
scikit-learn>=1.0.0
joblib>=1.1.0

# Computer vision (recommended)
opencv-python>=4.5.0
Pillow>=8.3.0

# Deep learning (for advanced features)
torch>=1.9.0
torchvision>=0.10.0
facenet-pytorch>=2.5.0

# Machine learning (optional)
xgboost>=1.5.0

# Visualization (optional)
matplotlib>=3.4.0
seaborn>=0.11.0

# Utilities (optional)
psutil>=5.8.0
scipy>=1.7.0

# GUI (optional)
# tkinter is usually included with Python

# Development (optional)
pytest>=6.2.0
black>=21.0.0
flake8>=3.9.0

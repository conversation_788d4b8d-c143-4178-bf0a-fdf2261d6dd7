"""
Optional GUI application for video upload and deepfake detection
"""

import os
import sys
import threading
import logging
from pathlib import Path

try:
    import tkinter as tk
    from tkinter import ttk, filedialog, messagebox, scrolledtext
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    print("⚠️  GUI not available - tkinter not found")

from config import initialize_system, DATASET_CONFIG
from predictor import predict_video, DeepfakePredictor
from extractor import get_feature_extractor

logger = logging.getLogger(__name__)

class DeepfakeDetectorGUI:
    """GUI application for deepfake detection"""
    
    def __init__(self):
        if not GUI_AVAILABLE:
            raise ImportError("GUI not available - tkinter required")
        
        self.root = tk.Tk()
        self.root.title("Deepfake Detection System v3.0")
        self.root.geometry("800x600")
        
        # Initialize system
        self.system_ready = False
        self.predictor = None
        
        # Create GUI elements
        self.create_widgets()
        
        # Initialize system in background
        self.initialize_system_async()
    
    def create_widgets(self):
        """Create GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="🤖 Deepfake Detection System", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Video selection frame
        video_frame = ttk.LabelFrame(main_frame, text="Video Selection", padding="10")
        video_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Video path
        self.video_path_var = tk.StringVar()
        ttk.Label(video_frame, text="Video File:").grid(row=0, column=0, sticky=tk.W)
        self.video_entry = ttk.Entry(video_frame, textvariable=self.video_path_var, width=50)
        self.video_entry.grid(row=0, column=1, padx=(10, 5), sticky=(tk.W, tk.E))
        
        self.browse_button = ttk.Button(video_frame, text="Browse", command=self.browse_video)
        self.browse_button.grid(row=0, column=2, padx=(5, 0))
        
        # Options frame
        options_frame = ttk.LabelFrame(main_frame, text="Options", padding="10")
        options_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Model selection
        ttk.Label(options_frame, text="Model:").grid(row=0, column=0, sticky=tk.W)
        self.model_var = tk.StringVar(value="best_random_forest")
        model_combo = ttk.Combobox(options_frame, textvariable=self.model_var, 
                                  values=["best_random_forest", "random_forest", "svm", "xgboost"])
        model_combo.grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        
        # Ensemble option
        self.ensemble_var = tk.BooleanVar()
        ensemble_check = ttk.Checkbutton(options_frame, text="Use Ensemble", 
                                        variable=self.ensemble_var)
        ensemble_check.grid(row=0, column=2, padx=(20, 0))
        
        # Action buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=(0, 10))
        
        self.analyze_button = ttk.Button(buttons_frame, text="🔍 Analyze Video", 
                                        command=self.analyze_video, state="disabled")
        self.analyze_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.batch_button = ttk.Button(buttons_frame, text="📁 Batch Analyze", 
                                      command=self.batch_analyze, state="disabled")
        self.batch_button.pack(side=tk.LEFT)
        
        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Results display
        self.results_text = scrolledtext.ScrolledText(results_frame, height=15, width=70)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Status bar
        self.status_var = tk.StringVar(value="Initializing system...")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        video_frame.columnconfigure(1, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
    
    def initialize_system_async(self):
        """Initialize system in background thread"""
        def init_worker():
            try:
                self.log_message("🚀 Initializing Deepfake Detection System...")
                
                if initialize_system():
                    self.log_message("✅ System initialized successfully")
                    
                    # Try to load predictor
                    try:
                        self.predictor = DeepfakePredictor()
                        self.log_message("✅ Predictor loaded successfully")
                        self.system_ready = True
                        
                        # Enable buttons
                        self.root.after(0, self.enable_buttons)
                        self.root.after(0, lambda: self.status_var.set("Ready"))
                        
                    except Exception as e:
                        self.log_message(f"⚠️  Predictor initialization failed: {e}")
                        self.log_message("💡 Train a model first using the command line interface")
                        self.root.after(0, lambda: self.status_var.set("No trained model found"))
                else:
                    self.log_message("❌ System initialization failed")
                    self.root.after(0, lambda: self.status_var.set("Initialization failed"))
                    
            except Exception as e:
                self.log_message(f"❌ Initialization error: {e}")
                self.root.after(0, lambda: self.status_var.set("Error"))
        
        thread = threading.Thread(target=init_worker, daemon=True)
        thread.start()
    
    def enable_buttons(self):
        """Enable action buttons"""
        self.analyze_button.config(state="normal")
        self.batch_button.config(state="normal")
    
    def log_message(self, message):
        """Add message to results display"""
        def update_text():
            self.results_text.insert(tk.END, message + "\n")
            self.results_text.see(tk.END)
        
        self.root.after(0, update_text)
    
    def browse_video(self):
        """Browse for video file"""
        filetypes = [
            ("Video files", " ".join(f"*{ext}" for ext in DATASET_CONFIG["supported_formats"])),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=filetypes
        )
        
        if filename:
            self.video_path_var.set(filename)
    
    def analyze_video(self):
        """Analyze single video"""
        if not self.system_ready:
            messagebox.showerror("Error", "System not ready. Please wait for initialization.")
            return
        
        video_path = self.video_path_var.get().strip()
        if not video_path:
            messagebox.showerror("Error", "Please select a video file.")
            return
        
        if not os.path.exists(video_path):
            messagebox.showerror("Error", "Video file not found.")
            return
        
        # Disable button during analysis
        self.analyze_button.config(state="disabled")
        self.status_var.set("Analyzing video...")
        
        def analyze_worker():
            try:
                self.log_message(f"\n🔍 Analyzing: {os.path.basename(video_path)}")
                
                # Make prediction
                result = predict_video(
                    video_path, 
                    model_name=self.model_var.get(),
                    use_ensemble=self.ensemble_var.get()
                )
                
                # Display results
                self.display_result(result, video_path)
                
            except Exception as e:
                self.log_message(f"❌ Analysis failed: {e}")
            finally:
                self.root.after(0, lambda: self.analyze_button.config(state="normal"))
                self.root.after(0, lambda: self.status_var.set("Ready"))
        
        thread = threading.Thread(target=analyze_worker, daemon=True)
        thread.start()
    
    def batch_analyze(self):
        """Analyze multiple videos in a directory"""
        if not self.system_ready:
            messagebox.showerror("Error", "System not ready. Please wait for initialization.")
            return
        
        directory = filedialog.askdirectory(title="Select Directory with Videos")
        if not directory:
            return
        
        # Disable button during analysis
        self.batch_button.config(state="disabled")
        self.status_var.set("Batch analyzing...")
        
        def batch_worker():
            try:
                from predictor import predict_batch
                
                self.log_message(f"\n📁 Batch analyzing directory: {directory}")
                
                results = predict_batch(
                    directory,
                    model_name=self.model_var.get(),
                    use_ensemble=self.ensemble_var.get()
                )
                
                # Display batch results
                self.display_batch_results(results)
                
            except Exception as e:
                self.log_message(f"❌ Batch analysis failed: {e}")
            finally:
                self.root.after(0, lambda: self.batch_button.config(state="normal"))
                self.root.after(0, lambda: self.status_var.set("Ready"))
        
        thread = threading.Thread(target=batch_worker, daemon=True)
        thread.start()
    
    def display_result(self, result, video_path):
        """Display single prediction result"""
        filename = os.path.basename(video_path)
        
        if result["status"] == "success":
            prediction = result["prediction"].upper()
            confidence = result["confidence"]
            
            if prediction == "REAL":
                icon = "🟢"
            elif prediction == "FAKE":
                icon = "🔴"
            else:
                icon = "🟡"
            
            self.log_message(f"{icon} RESULT: {prediction}")
            self.log_message(f"📊 Confidence: {confidence:.4f}")
            self.log_message(f"📈 Probabilities:")
            self.log_message(f"  Real: {result['probabilities']['real']:.4f}")
            self.log_message(f"  Fake: {result['probabilities']['fake']:.4f}")
            
            if "model_used" in result:
                self.log_message(f"🤖 Model: {result['model_used']}")
        else:
            self.log_message(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
    
    def display_batch_results(self, results):
        """Display batch prediction results"""
        if not results:
            self.log_message("❌ No results to display")
            return
        
        self.log_message(f"\n📊 BATCH RESULTS ({len(results)} videos)")
        self.log_message("-" * 50)
        
        for result in results:
            filename = result["filename"]
            if result["status"] == "success":
                prediction = result["prediction"].upper()
                confidence = result["confidence"]
                
                if prediction == "REAL":
                    icon = "🟢"
                elif prediction == "FAKE":
                    icon = "🔴"
                else:
                    icon = "🟡"
                
                self.log_message(f"{icon} {filename}: {prediction} ({confidence:.3f})")
            else:
                self.log_message(f"❌ {filename}: FAILED")
        
        # Summary
        successful = [r for r in results if r["status"] == "success"]
        if successful:
            real_count = len([r for r in successful if r["prediction"] == "real"])
            fake_count = len([r for r in successful if r["prediction"] == "fake"])
            
            self.log_message(f"\n📈 SUMMARY:")
            self.log_message(f"  Total: {len(results)}")
            self.log_message(f"  Real: {real_count}")
            self.log_message(f"  Fake: {fake_count}")
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()

def launch_gui():
    """Launch the GUI application"""
    if not GUI_AVAILABLE:
        print("❌ GUI not available - tkinter required")
        print("💡 Use the command line interface instead: python main.py")
        return False
    
    try:
        app = DeepfakeDetectorGUI()
        app.run()
        return True
    except Exception as e:
        print(f"❌ GUI launch failed: {e}")
        return False

if __name__ == "__main__":
    if GUI_AVAILABLE:
        launch_gui()
    else:
        print("❌ GUI not available")
        print("💡 Install tkinter or use command line: python main.py")

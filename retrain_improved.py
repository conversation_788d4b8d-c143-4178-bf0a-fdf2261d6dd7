#!/usr/bin/env python3
"""
Retrain model with improved settings
"""

import os
from config import initialize_system
from trainer import train_model

def retrain_with_improvements():
    """Retrain model with improved configuration"""
    print("🚀 RETRAINING WITH IMPROVED SETTINGS")
    print("="*50)
    
    if not initialize_system():
        print("❌ System initialization failed!")
        return False
    
    print("📊 Current improvements applied:")
    print("• Frame sample rate: 6 → 3 (more frames)")
    print("• Max faces per video: 25 → 50")
    print("• Detection thresholds: [0.6,0.7,0.7] → [0.5,0.6,0.6]")
    print("• Confidence threshold: 0.5 → 0.6")
    
    print("\n🤖 Training ensemble model...")
    success = train_model("ensemble")
    
    if success:
        print("\n✅ IMPROVED MODEL TRAINING COMPLETED!")
        print("📈 Your model should now have better accuracy")
        print("🎯 Test it with the same videos to see improvement")
        return True
    else:
        print("\n❌ Training failed!")
        return False

def test_improved_model():
    """Test the improved model"""
    print("\n🧪 TESTING IMPROVED MODEL")
    print("="*30)
    
    from predictor import predict_video
    
    # Test on a real video
    real_video = "dataset/real/image 2 (21).mp4"
    if os.path.exists(real_video):
        print(f"Testing on real video: {real_video}")
        result = predict_video(real_video, use_ensemble=True)
        print(f"Result: {result}")
    
    # Test on a fake video  
    fake_video = "dataset/fake/image 1.mp4"
    if os.path.exists(fake_video):
        print(f"Testing on fake video: {fake_video}")
        result = predict_video(fake_video, use_ensemble=True)
        print(f"Result: {result}")

if __name__ == "__main__":
    success = retrain_with_improvements()
    
    if success:
        print("\n💡 NEXT STEPS TO FURTHER IMPROVE:")
        print("1. 📹 Add more training videos (aim for 20+ of each type)")
        print("2. 🎯 Test on different types of deepfakes")
        print("3. 📊 Monitor performance and adjust thresholds")
        print("4. 🔄 Re-extract features with new settings if needed")
        
        # Uncomment to test immediately
        # test_improved_model()
    else:
        print("\n❌ Please check the error messages above")

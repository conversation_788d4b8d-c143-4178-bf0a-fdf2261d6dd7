
# IMPROVED CONFIGURATION FOR BETTER DETECTION

# Enhanced Feature Extraction
EXTRACTION_CONFIG_IMPROVED = {
    'frame_sample_rate': 3,
    'max_faces_per_video': 50,
    'detection_thresholds': [0.5, 0.6, 0.6],
    'enable_enhancement': True,
    'enable_denoising': True,
    'enable_sharpening': True,
    'target_face_size': 224,
    'normalize_features': True,
}

# Enhanced Training Configuration  
TRAINING_CONFIG_IMPROVED = {
    'enable_ensemble': True,
    'ensemble_models': ['random_forest', 'svm', 'xgboost'],
    'cross_validation_folds': 5,
    'test_size': 0.2,
    'random_state': 42,
    'enable_pca': True,
    'pca_components': 512,
}

# Enhanced Prediction Configuration
PREDICTION_CONFIG_IMPROVED = {
    'confidence_threshold': 0.6,
    'ensemble_voting': 'soft',
    'default_model': 'best_ensemble',
    'enable_uncertainty': True,
}

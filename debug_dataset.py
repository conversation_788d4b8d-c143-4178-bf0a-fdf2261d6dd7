#!/usr/bin/env python3
"""
Debug script to check dataset structure
"""

import os
from config import DATASET_PATH, DATASET_CONFIG
from utils import DatasetValidator

def debug_dataset():
    print("=== DATASET DEBUG ===")
    print(f"DATASET_PATH: {DATASET_PATH}")
    print(f"DATASET_PATH exists: {os.path.exists(DATASET_PATH)}")
    
    real_path = os.path.join(DATASET_PATH, "real")
    fake_path = os.path.join(DATASET_PATH, "fake")
    
    print(f"Real path: {real_path}")
    print(f"Real path exists: {os.path.exists(real_path)}")
    
    print(f"Fake path: {fake_path}")
    print(f"Fake path exists: {os.path.exists(fake_path)}")
    
    if os.path.exists(DATASET_PATH):
        print(f"Contents of {DATASET_PATH}:")
        for item in os.listdir(DATASET_PATH):
            item_path = os.path.join(DATASET_PATH, item)
            print(f"  {item} ({'dir' if os.path.isdir(item_path) else 'file'})")
    
    if os.path.exists(real_path):
        print(f"Contents of {real_path}:")
        for item in os.listdir(real_path):
            print(f"  {item}")
    
    if os.path.exists(fake_path):
        print(f"Contents of {fake_path}:")
        for item in os.listdir(fake_path):
            print(f"  {item}")
    
    print(f"Supported formats: {DATASET_CONFIG['supported_formats']}")
    
    # Test validator
    validator = DatasetValidator()
    issues = validator.validate_dataset_structure(DATASET_PATH)
    print(f"Validation issues: {issues}")

if __name__ == "__main__":
    debug_dataset()

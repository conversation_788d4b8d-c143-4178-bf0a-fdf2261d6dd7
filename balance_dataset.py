#!/usr/bin/env python3
"""
Check and balance the training dataset
"""

import os
import numpy as np
from trainer import <PERSON><PERSON>rain<PERSON>

def analyze_current_dataset():
    """Analyze the current training dataset"""
    print("📊 CURRENT DATASET ANALYSIS")
    print("="*40)
    
    trainer = ModelTrainer()
    X, y, filenames = trainer.feature_loader.load_features()
    
    if X is None:
        print("❌ No features loaded!")
        return
    
    real_count = np.sum(y == 0)
    fake_count = np.sum(y == 1)
    
    print(f"📹 Total samples: {len(X)}")
    print(f"🟢 Real samples: {real_count}")
    print(f"🔴 Fake samples: {fake_count}")
    print(f"⚖️  Ratio (Real:Fake): 1:{fake_count/real_count:.1f}")
    
    if fake_count > real_count * 1.5:
        print("⚠️  IMBALANCED: Too many fake samples!")
        print(f"💡 Recommendation: Add {fake_count - real_count} more real videos")
    elif real_count > fake_count * 1.5:
        print("⚠️  IMBALANCED: Too many real samples!")
        print(f"💡 Recommendation: Add {real_count - fake_count} more fake videos")
    else:
        print("✅ Dataset is reasonably balanced")
    
    return real_count, fake_count

def suggest_improvements():
    """Suggest specific improvements"""
    print("\n🎯 IMPROVEMENT STRATEGIES")
    print("="*40)
    
    print("1. 📹 BALANCE DATASET:")
    print("   • Add 8 more real videos to match 16 fake videos")
    print("   • Or remove 8 fake videos to match 8 real videos")
    
    print("\n2. 🔧 ADJUST MODEL SETTINGS:")
    print("   • Use class weights to handle imbalance")
    print("   • Adjust decision threshold")
    print("   • Try different algorithms")
    
    print("\n3. 📊 IMPROVE DATA QUALITY:")
    print("   • Ensure real videos are clearly authentic")
    print("   • Ensure fake videos are obvious deepfakes")
    print("   • Add variety in lighting, angles, people")

def create_balanced_training_script():
    """Create a script for balanced training"""
    script_content = '''#!/usr/bin/env python3
"""
Train model with balanced dataset and class weights
"""

from trainer import ModelTrainer
from config import initialize_system
import numpy as np

def train_balanced_model():
    """Train model with class balancing"""
    print("🎯 TRAINING BALANCED MODEL")
    print("="*40)
    
    if not initialize_system():
        print("❌ System initialization failed!")
        return False
    
    # Create custom trainer with balanced settings
    trainer = ModelTrainer()
    
    # Load data
    X, y, filenames = trainer.feature_loader.load_features()
    if X is None:
        print("❌ No features available!")
        return False
    
    # Check balance
    real_count = np.sum(y == 0)
    fake_count = np.sum(y == 1)
    print(f"Real: {real_count}, Fake: {fake_count}")
    
    # Calculate class weights
    total = len(y)
    weight_real = total / (2 * real_count)
    weight_fake = total / (2 * fake_count)
    
    print(f"Class weights - Real: {weight_real:.2f}, Fake: {weight_fake:.2f}")
    
    # Train with balanced settings
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.svm import SVC
    
    # Prepare data
    X_train, X_test, y_train, y_test, scaler, pca = trainer.prepare_data(X, y)
    
    # Train Random Forest with class weights
    rf_balanced = RandomForestClassifier(
        n_estimators=100,
        class_weight='balanced',
        random_state=42
    )
    rf_balanced.fit(X_train, y_train)
    
    # Train SVM with class weights
    svm_balanced = SVC(
        class_weight='balanced',
        probability=True,
        random_state=42
    )
    svm_balanced.fit(X_train, y_train)
    
    # Evaluate both
    rf_score = rf_balanced.score(X_test, y_test)
    svm_score = svm_balanced.score(X_test, y_test)
    
    print(f"Balanced RF accuracy: {rf_score:.3f}")
    print(f"Balanced SVM accuracy: {svm_score:.3f}")
    
    # Save the better model
    if rf_score >= svm_score:
        trainer.save_model(rf_balanced, scaler, "balanced_rf", pca)
        print("✅ Saved balanced Random Forest model")
    else:
        trainer.save_model(svm_balanced, scaler, "balanced_svm", pca)
        print("✅ Saved balanced SVM model")
    
    return True

if __name__ == "__main__":
    train_balanced_model()
'''
    
    with open('train_balanced.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ Created train_balanced.py")

if __name__ == "__main__":
    real_count, fake_count = analyze_current_dataset()
    suggest_improvements()
    create_balanced_training_script()
    
    print(f"\n🚀 NEXT STEPS:")
    print("1. Run: python train_balanced.py")
    print("2. Test the balanced model")
    print("3. Add more real videos if possible")

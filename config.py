"""
Centralized configuration for Deepfake Detection System
"""

import os
import logging

# ===== PROJECT PATHS =====
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
DATASET_PATH = os.path.join(PROJECT_ROOT, "dataset")
FEATURES_PATH = os.path.join(PROJECT_ROOT, "features")
MODELS_PATH = os.path.join(PROJECT_ROOT, "models")

# ===== DATASET CONFIGURATION =====
DATASET_CONFIG = {
    "real_videos_path": os.path.join(DATASET_PATH, "real"),
    "fake_videos_path": os.path.join(DATASET_PATH, "fake"),
    "supported_formats": ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.webm', '.m4v'],
    "min_video_duration": 1.0,  # seconds
    "max_video_duration": 300.0,  # seconds
}

# ===== FEATURE EXTRACTION CONFIGURATION =====
EXTRACTION_CONFIG = {
    # Face detection settings
    "face_detection_model": "mtcnn",  # or "opencv" for basic detection
    "min_face_size": 20,
    "face_margin": 10,
    "detection_thresholds": [0.5, 0.6, 0.6],  # Lower thresholds for better detection

    # Video processing settings
    "frame_sample_rate": 3,  # Process every 3rd frame (more frames)
    "max_faces_per_video": 50,  # More faces for longer videos
    "target_face_size": 224,
    
    # Quality enhancement
    "enable_enhancement": True,
    "enable_denoising": True,
    "enable_sharpening": True,
    "enable_contrast_enhancement": True,
    
    # Feature extraction
    "feature_extractor": "resnext",  # "resnext", "resnet", "basic"
    "feature_dimension": 2048,  # ResNeXt-50 output dimension
    "normalize_features": True,
    "apply_pca": False,
    "pca_components": 512,
}

# ===== MODEL TRAINING CONFIGURATION =====
TRAINING_CONFIG = {
    # Data splitting
    "test_size": 0.2,
    "validation_size": 0.1,
    "random_state": 42,
    "stratify": True,
    
    # Model selection
    "default_classifier": "random_forest",  # "random_forest", "svm", "xgboost", "neural_network"
    "enable_ensemble": True,
    "ensemble_models": ["random_forest", "svm", "xgboost"],
    
    # Random Forest settings
    "rf_n_estimators": 200,
    "rf_max_depth": 15,
    "rf_min_samples_split": 5,
    "rf_min_samples_leaf": 2,
    
    # SVM settings
    "svm_kernel": "rbf",
    "svm_C": 1.0,
    "svm_gamma": "scale",
    
    # Neural Network settings
    "nn_hidden_layers": [512, 256, 128],
    "nn_activation": "relu",
    "nn_max_iter": 1000,
    
    # Training optimization
    "cross_validation_folds": 5,
    "hyperparameter_tuning": True,
    "feature_selection": True,
    "feature_scaling": True,
}

# ===== PREDICTION CONFIGURATION =====
PREDICTION_CONFIG = {
    "confidence_threshold": 0.6,  # Higher threshold for more confident predictions
    "ensemble_voting": "soft",  # "hard" or "soft"
    "return_probabilities": True,
    "batch_size": 32,
    "enable_preprocessing": True,
}

# ===== SYSTEM CONFIGURATION =====
SYSTEM_CONFIG = {
    # Device settings
    "device": "cpu",  # Force CPU to avoid CUDA compatibility issues
    "num_workers": 4,
    "memory_limit_gb": 8,
    
    # Logging
    "log_level": "INFO",
    "log_file": os.path.join(PROJECT_ROOT, "deepfake_detector.log"),
    "enable_console_logging": True,
    
    # Performance
    "enable_multiprocessing": True,
    "chunk_size": 100,
    "cache_features": True,
    
    # Quality settings
    "min_video_resolution": 144,
    "max_video_resolution": 4096,
    "auto_quality_adjustment": True,
}

# ===== DEPENDENCY TRACKING =====
DEPENDENCIES = {
    "required": {
        "numpy": ">=1.21.0",
        "scikit-learn": ">=1.0.0",
        "joblib": ">=1.1.0",
    },
    "optional": {
        "opencv-python": ">=4.5.0",
        "torch": ">=1.9.0",
        "torchvision": ">=0.10.0",
        "facenet-pytorch": ">=2.5.0",
        "pillow": ">=8.3.0",
        "matplotlib": ">=3.4.0",
        "seaborn": ">=0.11.0",
        "xgboost": ">=1.5.0",
    }
}

# ===== RUNTIME DEPENDENCY DETECTION =====
AVAILABLE_FEATURES = {
    "basic_extraction": True,  # Always available
    "advanced_face_detection": False,
    "deep_learning_features": False,
    "gpu_acceleration": False,
    "visualization": False,
    "ensemble_models": False,
}

def check_dependencies():
    """Check which dependencies are available and update feature flags"""
    global AVAILABLE_FEATURES
    
    # Check NumPy (required)
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__}")
    except ImportError:
        print("❌ NumPy not found - REQUIRED")
        return False
    
    # Check scikit-learn (required)
    try:
        import sklearn
        print(f"✅ scikit-learn {sklearn.__version__}")
    except ImportError:
        print("❌ scikit-learn not found - REQUIRED")
        return False
    
    # Check OpenCV (optional)
    try:
        import cv2
        print(f"✅ OpenCV {cv2.__version__}")
        AVAILABLE_FEATURES["advanced_face_detection"] = True
    except ImportError:
        print("⚠️  OpenCV not found - using basic extraction")
    
    # Check PyTorch (optional)
    try:
        import torch
        import torchvision
        print(f"✅ PyTorch {torch.__version__}")
        print(f"✅ TorchVision {torchvision.__version__}")
        AVAILABLE_FEATURES["deep_learning_features"] = True
        
        if torch.cuda.is_available():
            print("ℹ️  CUDA available but using CPU for stability")
        
    except ImportError:
        print("⚠️  PyTorch not found - using basic features")
    
    # Check FaceNet-PyTorch (optional)
    try:
        import facenet_pytorch
        print("✅ FaceNet-PyTorch available")
        AVAILABLE_FEATURES["advanced_face_detection"] = True
    except ImportError:
        print("⚠️  FaceNet-PyTorch not found")
    
    # Check visualization libraries (optional)
    try:
        import matplotlib
        import seaborn
        print("✅ Visualization libraries available")
        AVAILABLE_FEATURES["visualization"] = True
    except ImportError:
        print("⚠️  Visualization libraries not found")
    
    # Check XGBoost (optional)
    try:
        import xgboost
        print("✅ XGBoost available")
        AVAILABLE_FEATURES["ensemble_models"] = True
    except ImportError:
        print("⚠️  XGBoost not found - limited ensemble options")
    
    return True

def setup_logging():
    """Setup logging configuration"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(SYSTEM_CONFIG["log_file"])
    os.makedirs(log_dir, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, SYSTEM_CONFIG["log_level"]),
        format=log_format,
        handlers=[
            logging.FileHandler(SYSTEM_CONFIG["log_file"]),
            logging.StreamHandler() if SYSTEM_CONFIG["enable_console_logging"] else logging.NullHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def create_directories():
    """Create necessary project directories"""
    directories = [
        DATASET_PATH,
        DATASET_CONFIG["real_videos_path"],
        DATASET_CONFIG["fake_videos_path"],
        FEATURES_PATH,
        MODELS_PATH,
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("📁 Project directories created")

def get_model_path(model_name):
    """Get full path for a model file"""
    return os.path.join(MODELS_PATH, f"{model_name}.pkl")

def get_feature_path(label):
    """Get full path for feature files"""
    return os.path.join(FEATURES_PATH, label)

def print_configuration():
    """Print current configuration summary"""
    print("\n" + "="*50)
    print("🔧 DEEPFAKE DETECTOR CONFIGURATION")
    print("="*50)
    
    print(f"📁 Project Root: {PROJECT_ROOT}")
    print(f"📹 Dataset Path: {DATASET_PATH}")
    print(f"🔧 Features Path: {FEATURES_PATH}")
    print(f"🤖 Models Path: {MODELS_PATH}")
    
    print(f"\n📊 Available Features:")
    for feature, available in AVAILABLE_FEATURES.items():
        status = "✅" if available else "❌"
        print(f"  {status} {feature.replace('_', ' ').title()}")
    
    print(f"\n⚙️  Key Settings:")
    print(f"  Device: {SYSTEM_CONFIG['device']}")
    print(f"  Feature Extractor: {EXTRACTION_CONFIG['feature_extractor']}")
    print(f"  Default Classifier: {TRAINING_CONFIG['default_classifier']}")
    print(f"  Frame Sample Rate: {EXTRACTION_CONFIG['frame_sample_rate']}")

def initialize_system():
    """Initialize the complete system"""
    print("🚀 Initializing Deepfake Detection System...")
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Required dependencies missing!")
        return False
    
    # Create directories
    create_directories()
    
    # Setup logging
    logger = setup_logging()
    logger.info("System initialized successfully")
    
    # Print configuration
    print_configuration()
    
    print("✅ System ready!")
    return True

if __name__ == "__main__":
    initialize_system()

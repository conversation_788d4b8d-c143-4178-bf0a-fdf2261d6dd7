2025-07-26 20:25:01,721 - __main__ - INFO - System initialized successfully
2025-07-26 20:25:31,622 - config - INFO - System initialized successfully
2025-07-26 20:25:31,628 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 20:32:45,025 - config - INFO - System initialized successfully
2025-07-26 20:32:45,030 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 20:38:01,744 - config - INFO - System initialized successfully
2025-07-26 20:38:01,752 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 20:48:44,798 - config - INFO - System initialized successfully
2025-07-26 20:48:44,806 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 20:49:30,586 - config - INFO - System initialized successfully
2025-07-26 20:49:30,593 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 20:52:27,790 - config - INFO - System initialized successfully
2025-07-26 20:52:27,797 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 20:52:50,003 - extractor - INFO - Starting batch feature extraction for real videos
2025-07-26 20:57:01,504 - config - INFO - System initialized successfully
2025-07-26 20:57:01,511 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 20:57:27,414 - extractor - INFO - Starting batch feature extraction for real videos
2025-07-26 20:58:10,263 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 20:58:10,264 - extractor - INFO - Starting feature extraction for image 2 (101).mp4
2025-07-26 20:58:10,454 - extractor - INFO - Processing video: 373 frames, 30.0 FPS
2025-07-26 20:58:49,353 - config - INFO - System initialized successfully
2025-07-26 20:58:49,363 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 20:58:54,756 - extractor - INFO - Starting batch feature extraction for real videos
2025-07-26 20:58:55,851 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 20:58:55,853 - extractor - INFO - Starting feature extraction for image 2 (101).mp4
2025-07-26 20:58:55,859 - extractor - INFO - Processing video: 373 frames, 30.0 FPS
2025-07-26 20:58:57,210 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 20:59:04,530 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 20:59:04,531 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (101).mp4
2025-07-26 20:59:04,534 - extractor - INFO - Saved features: image_2_(101)_real.npy
2025-07-26 20:59:04,537 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-26 20:59:04,544 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-26 20:59:52,773 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 20:59:59,751 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 20:59:59,752 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (101).mp4
2025-07-26 20:59:59,755 - extractor - INFO - Saved features: image_2_(101)_real.npy
2025-07-26 20:59:59,756 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-26 20:59:59,760 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-26 21:00:15,285 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:00:22,582 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:00:22,583 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (21).mp4
2025-07-26 21:00:22,586 - extractor - INFO - Saved features: image_2_(21)_real.npy
2025-07-26 21:00:22,587 - extractor - INFO - Starting feature extraction for image 2 (4).mp4
2025-07-26 21:00:22,594 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-26 21:01:37,569 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:01:39,964 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:01:45,132 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:01:45,133 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (4).mp4
2025-07-26 21:01:45,137 - extractor - INFO - Saved features: image_2_(4)_real.npy
2025-07-26 21:01:45,138 - extractor - INFO - Starting feature extraction for image 2 (61).mp4
2025-07-26 21:01:45,145 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-26 21:01:57,074 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:01:57,075 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (21).mp4
2025-07-26 21:01:57,078 - extractor - INFO - Saved features: image_2_(21)_real.npy
2025-07-26 21:01:57,079 - extractor - INFO - Starting feature extraction for image 2 (4).mp4
2025-07-26 21:01:57,083 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-26 21:03:24,553 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:03:35,384 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:03:40,977 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:03:40,978 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (61).mp4
2025-07-26 21:03:40,981 - extractor - INFO - Saved features: image_2_(61)_real.npy
2025-07-26 21:03:40,983 - extractor - INFO - Batch extraction completed: 4 videos processed
2025-07-26 21:03:40,986 - extractor - INFO - Starting batch feature extraction for fake videos
2025-07-26 21:03:42,038 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 21:03:42,039 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-26 21:03:42,045 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-26 21:03:48,998 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:03:48,999 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (4).mp4
2025-07-26 21:03:49,002 - extractor - INFO - Saved features: image_2_(4)_real.npy
2025-07-26 21:03:49,003 - extractor - INFO - Starting feature extraction for image 2 (61).mp4
2025-07-26 21:03:49,008 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-26 21:05:39,265 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:05:44,418 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:05:51,143 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:05:51,145 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 1.mp4
2025-07-26 21:05:51,148 - extractor - INFO - Saved features: image_1_fake.npy
2025-07-26 21:05:51,149 - extractor - INFO - Starting feature extraction for image 2 (10).mp4
2025-07-26 21:05:51,153 - extractor - INFO - Processing video: 349 frames, 30.0 FPS
2025-07-26 21:05:52,314 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:05:52,315 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (61).mp4
2025-07-26 21:05:52,317 - extractor - INFO - Saved features: image_2_(61)_real.npy
2025-07-26 21:05:52,319 - extractor - INFO - Batch extraction completed: 4 videos processed
2025-07-26 21:05:52,325 - extractor - INFO - Starting batch feature extraction for fake videos
2025-07-26 21:05:54,027 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 21:05:54,028 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-26 21:05:54,032 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-26 21:07:09,456 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:07:12,087 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:07:18,836 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:07:18,837 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (10).mp4
2025-07-26 21:07:18,840 - extractor - INFO - Saved features: image_2_(10)_fake.npy
2025-07-26 21:07:18,841 - extractor - INFO - Starting feature extraction for image 2 (8).mp4
2025-07-26 21:07:18,847 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-26 21:07:19,570 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:07:19,571 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 1.mp4
2025-07-26 21:07:19,573 - extractor - INFO - Saved features: image_1_fake.npy
2025-07-26 21:07:19,574 - extractor - INFO - Starting feature extraction for image 2 (10).mp4
2025-07-26 21:07:19,578 - extractor - INFO - Processing video: 349 frames, 30.0 FPS
2025-07-26 21:08:12,556 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:08:19,270 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:08:19,271 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (10).mp4
2025-07-26 21:08:19,273 - extractor - INFO - Saved features: image_2_(10)_fake.npy
2025-07-26 21:08:19,275 - extractor - INFO - Starting feature extraction for image 2 (8).mp4
2025-07-26 21:08:19,279 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-26 21:08:25,926 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:08:32,844 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:08:32,845 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (8).mp4
2025-07-26 21:08:32,848 - extractor - INFO - Saved features: image_2_(8)_fake.npy
2025-07-26 21:08:32,849 - extractor - INFO - Starting feature extraction for image 2 (9).mp4
2025-07-26 21:08:32,855 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-26 21:09:47,188 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:09:54,360 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:09:54,361 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (9).mp4
2025-07-26 21:09:54,364 - extractor - INFO - Saved features: image_2_(9)_fake.npy
2025-07-26 21:09:54,365 - extractor - INFO - Batch extraction completed: 4 videos processed
2025-07-26 21:09:54,368 - utils - INFO - Operation 'feature_extraction' took 746.96 seconds
2025-07-26 21:09:59,333 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:10:04,637 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:10:04,637 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (8).mp4
2025-07-26 21:10:04,639 - extractor - INFO - Saved features: image_2_(8)_fake.npy
2025-07-26 21:10:04,640 - extractor - INFO - Starting feature extraction for image 2 (9).mp4
2025-07-26 21:10:04,643 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-26 21:11:00,482 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 21:11:05,611 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 21:11:05,611 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (9).mp4
2025-07-26 21:11:05,613 - extractor - INFO - Saved features: image_2_(9)_fake.npy
2025-07-26 21:11:05,614 - extractor - INFO - Batch extraction completed: 4 videos processed
2025-07-26 21:11:05,616 - utils - INFO - Operation 'feature_extraction' took 730.86 seconds
2025-07-26 23:28:36,379 - trainer - INFO - Initialized FeatureLoader
2025-07-26 23:28:36,379 - trainer - INFO - Initialized ModelTrainer
2025-07-26 23:28:36,380 - trainer - INFO - Starting model training: random_forest
2025-07-26 23:28:36,400 - trainer - INFO - Loaded 8 real samples
2025-07-26 23:28:36,444 - trainer - INFO - Loaded 16 fake samples
2025-07-26 23:28:36,445 - trainer - WARNING - Inconsistent feature shapes detected: {150528, 3072}
2025-07-26 23:28:36,446 - trainer - INFO - Normalized all features to shape: 3072
2025-07-26 23:28:36,447 - trainer - INFO - Final dataset: 24 samples, 3072 features
2025-07-26 23:28:36,449 - trainer - INFO - Class distribution: Real=8, Fake=16
2025-07-26 23:28:36,499 - trainer - INFO - Applied feature scaling
2025-07-26 23:28:38,337 - trainer - INFO - Trained Random Forest classifier
2025-07-26 23:28:38,414 - trainer - INFO - random_forest Results:
2025-07-26 23:28:38,414 - trainer - INFO -   Accuracy: 0.4000
2025-07-26 23:28:38,449 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.33      0.50      0.40         2
        Fake       0.50      0.33      0.40         3

    accuracy                           0.40         5
   macro avg       0.42      0.42      0.40         5
weighted avg       0.43      0.40      0.40         5

2025-07-26 23:28:38,569 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-26 23:28:38,573 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-26 23:28:38,573 - trainer - INFO - Training completed: random_forest
2025-07-26 23:28:38,574 - utils - INFO - Operation 'model_training' took 2.20 seconds
2025-07-26 23:28:49,042 - trainer - INFO - Initialized FeatureLoader
2025-07-26 23:28:49,042 - trainer - INFO - Initialized ModelTrainer
2025-07-26 23:28:49,042 - trainer - INFO - Starting model training: ensemble
2025-07-26 23:28:49,047 - trainer - INFO - Loaded 8 real samples
2025-07-26 23:28:49,066 - trainer - INFO - Loaded 16 fake samples
2025-07-26 23:28:49,066 - trainer - WARNING - Inconsistent feature shapes detected: {150528, 3072}
2025-07-26 23:28:49,067 - trainer - INFO - Normalized all features to shape: 3072
2025-07-26 23:28:49,068 - trainer - INFO - Final dataset: 24 samples, 3072 features
2025-07-26 23:28:49,068 - trainer - INFO - Class distribution: Real=8, Fake=16
2025-07-26 23:28:49,077 - trainer - INFO - Applied feature scaling
2025-07-26 23:28:49,582 - trainer - INFO - Trained Random Forest classifier
2025-07-26 23:28:49,596 - trainer - INFO - Trained SVM classifier
2025-07-26 23:28:50,097 - trainer - INFO - Trained XGBoost classifier
2025-07-26 23:28:50,180 - trainer - INFO - random_forest Results:
2025-07-26 23:28:50,181 - trainer - INFO -   Accuracy: 0.4000
2025-07-26 23:28:50,202 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.33      0.50      0.40         2
        Fake       0.50      0.33      0.40         3

    accuracy                           0.40         5
   macro avg       0.42      0.42      0.40         5
weighted avg       0.43      0.40      0.40         5

2025-07-26 23:28:50,207 - trainer - INFO - svm Results:
2025-07-26 23:28:50,207 - trainer - INFO -   Accuracy: 0.6000
2025-07-26 23:28:50,231 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.00      0.00      0.00         2
        Fake       0.60      1.00      0.75         3

    accuracy                           0.60         5
   macro avg       0.30      0.50      0.38         5
weighted avg       0.36      0.60      0.45         5

2025-07-26 23:28:50,240 - trainer - INFO - xgboost Results:
2025-07-26 23:28:50,240 - trainer - INFO -   Accuracy: 0.0000
2025-07-26 23:28:50,265 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.00      0.00      0.00       2.0
        Fake       0.00      0.00      0.00       3.0

    accuracy                           0.00       5.0
   macro avg       0.00      0.00      0.00       5.0
weighted avg       0.00      0.00      0.00       5.0

2025-07-26 23:28:50,268 - trainer - INFO - Best individual model: svm (Accuracy: 0.6000)
2025-07-26 23:28:50,278 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm.pkl
2025-07-26 23:28:50,283 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm_scaler.pkl
2025-07-26 23:28:50,455 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-26 23:28:50,458 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-26 23:28:50,501 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\svm.pkl
2025-07-26 23:28:50,505 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\svm_scaler.pkl
2025-07-26 23:28:50,521 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\xgboost.pkl
2025-07-26 23:28:50,527 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\xgboost_scaler.pkl
2025-07-26 23:28:50,528 - trainer - INFO - Ensemble training completed
2025-07-26 23:28:50,529 - utils - INFO - Operation 'model_training' took 1.49 seconds
2025-07-26 23:30:43,293 - predictor - INFO - Initializing DeepfakePredictor with model: best_random_forest
2025-07-26 23:30:43,391 - predictor - INFO - Loaded fallback model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-26 23:30:43,393 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-26 23:30:44,272 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 23:30:44,273 - predictor - INFO - Starting batch prediction for directory: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real
2025-07-26 23:30:44,274 - predictor - INFO - Found 4 videos to analyze
2025-07-26 23:30:44,275 - predictor - INFO - Processing video 1/4: image 2 (101).mp4
2025-07-26 23:30:44,276 - predictor - INFO - Analyzing video: image 2 (101).mp4
2025-07-26 23:30:44,277 - extractor - INFO - Starting feature extraction for image 2 (101).mp4
2025-07-26 23:30:44,284 - extractor - INFO - Processing video: 373 frames, 30.0 FPS
2025-07-26 23:31:23,759 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:31:28,967 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:31:28,968 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (101).mp4
2025-07-26 23:31:29,105 - predictor - INFO - Prediction: real (confidence: 0.8036)
2025-07-26 23:31:29,105 - predictor - INFO - Processing video 2/4: image 2 (21).mp4
2025-07-26 23:31:29,106 - predictor - INFO - Analyzing video: image 2 (21).mp4
2025-07-26 23:31:29,107 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-26 23:31:29,111 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-26 23:32:22,726 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:32:27,844 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:32:27,844 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (21).mp4
2025-07-26 23:32:27,979 - predictor - INFO - Prediction: real (confidence: 0.7350)
2025-07-26 23:32:27,979 - predictor - INFO - Processing video 3/4: image 2 (4).mp4
2025-07-26 23:32:27,980 - predictor - INFO - Analyzing video: image 2 (4).mp4
2025-07-26 23:32:27,980 - extractor - INFO - Starting feature extraction for image 2 (4).mp4
2025-07-26 23:32:27,984 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-26 23:33:25,325 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:33:30,659 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:33:30,659 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (4).mp4
2025-07-26 23:33:30,785 - predictor - INFO - Prediction: real (confidence: 0.6172)
2025-07-26 23:33:30,786 - predictor - INFO - Processing video 4/4: image 2 (61).mp4
2025-07-26 23:33:30,786 - predictor - INFO - Analyzing video: image 2 (61).mp4
2025-07-26 23:33:30,787 - extractor - INFO - Starting feature extraction for image 2 (61).mp4
2025-07-26 23:33:30,790 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-26 23:34:26,706 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:34:31,848 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:34:31,848 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (61).mp4
2025-07-26 23:34:31,979 - predictor - INFO - Prediction: real (confidence: 0.7646)
2025-07-26 23:34:31,980 - predictor - INFO - Batch prediction completed:
2025-07-26 23:34:31,980 - predictor - INFO -   Total videos: 4
2025-07-26 23:34:31,980 - predictor - INFO -   Successful: 4
2025-07-26 23:34:31,981 - predictor - INFO -   Real: 4
2025-07-26 23:34:31,981 - predictor - INFO -   Fake: 0
2025-07-26 23:34:31,982 - predictor - INFO -   Uncertain: 0
2025-07-26 23:34:31,987 - utils - INFO - Operation 'prediction' took 228.69 seconds
2025-07-26 23:35:37,322 - config - INFO - System initialized successfully
2025-07-26 23:35:37,418 - predictor - INFO - Initializing DeepfakePredictor with model: best_random_forest
2025-07-26 23:35:37,562 - predictor - INFO - Loaded fallback model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-26 23:35:37,564 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-26 23:35:38,245 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 23:42:17,774 - predictor - INFO - Initializing DeepfakePredictor with model: best_random_forest
2025-07-26 23:42:17,877 - predictor - INFO - Loaded fallback model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-26 23:42:17,879 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-26 23:42:18,681 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 23:42:18,681 - predictor - INFO - Analyzing video: image 1 (72).mp4
2025-07-26 23:42:18,682 - extractor - INFO - Starting feature extraction for image 1 (72).mp4
2025-07-26 23:42:18,687 - extractor - INFO - Processing video: 400 frames, 30.0 FPS
2025-07-26 23:43:27,390 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:43:35,350 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:43:35,350 - extractor - INFO - Successfully extracted features from F:/DatasetD/Celeb-synthesis/image 1 (72).mp4
2025-07-26 23:43:35,560 - predictor - INFO - Prediction: real (confidence: 0.6302)
2025-07-26 23:49:56,775 - config - INFO - System initialized successfully
2025-07-26 23:49:57,736 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 23:49:57,738 - extractor - INFO - Starting feature extraction for image 2 (101).mp4
2025-07-26 23:49:57,750 - extractor - INFO - Processing video: 373 frames, 30.0 FPS
2025-07-26 23:50:39,948 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:50:46,142 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:50:46,142 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (101).mp4
2025-07-26 23:50:46,143 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-26 23:50:46,148 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-26 23:51:45,607 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:51:52,110 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:51:52,110 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (21).mp4
2025-07-26 23:51:52,112 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-26 23:51:52,118 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-26 23:53:14,898 - config - INFO - System initialized successfully
2025-07-26 23:53:14,905 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-26 23:53:56,563 - predictor - INFO - Initializing DeepfakePredictor with model: best_random_forest
2025-07-26 23:53:57,114 - predictor - INFO - Loaded fallback model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-26 23:53:57,117 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-26 23:53:57,857 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 23:53:57,857 - predictor - INFO - Analyzing video: image 2 (21).mp4
2025-07-26 23:53:57,858 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-26 23:53:57,866 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-26 23:54:55,829 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:55:01,621 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:55:01,621 - extractor - INFO - Successfully extracted features from dataset/real/image 2 (21).mp4
2025-07-26 23:55:01,768 - predictor - INFO - Prediction: real (confidence: 0.7350)
2025-07-26 23:55:01,771 - utils - INFO - Operation 'prediction' took 65.21 seconds
2025-07-26 23:56:24,703 - predictor - INFO - Initializing DeepfakePredictor with model: best_random_forest
2025-07-26 23:56:24,942 - predictor - INFO - Loaded fallback model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-26 23:56:24,949 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-26 23:56:25,727 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-26 23:56:25,729 - predictor - INFO - Analyzing video: image 1.mp4
2025-07-26 23:56:25,730 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-26 23:56:25,735 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-26 23:57:32,006 - extractor - INFO - Extracted 25 faces, success rate: 104.2%
2025-07-26 23:57:38,085 - extractor - INFO - Extracted 3072 ResNeXt features from 25 faces
2025-07-26 23:57:38,085 - extractor - INFO - Successfully extracted features from dataset/fake/image 1.mp4
2025-07-26 23:57:38,218 - predictor - INFO - Prediction: real (confidence: 0.6164)
2025-07-26 23:57:38,224 - utils - INFO - Operation 'prediction' took 73.52 seconds
2025-07-27 00:00:48,847 - config - INFO - System initialized successfully
2025-07-27 00:00:48,855 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-27 00:04:08,448 - config - INFO - System initialized successfully
2025-07-27 00:04:08,455 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-27 00:05:52,511 - config - INFO - System initialized successfully
2025-07-27 00:05:52,519 - trainer - INFO - Initialized FeatureLoader
2025-07-27 00:05:52,521 - trainer - INFO - Initialized ModelTrainer
2025-07-27 00:05:52,521 - trainer - INFO - Starting model training: ensemble
2025-07-27 00:05:52,542 - trainer - INFO - Loaded 8 real samples
2025-07-27 00:05:52,586 - trainer - INFO - Loaded 16 fake samples
2025-07-27 00:05:52,589 - trainer - WARNING - Inconsistent feature shapes detected: {150528, 3072}
2025-07-27 00:05:52,590 - trainer - INFO - Normalized all features to shape: 3072
2025-07-27 00:05:52,591 - trainer - INFO - Final dataset: 24 samples, 3072 features
2025-07-27 00:05:52,592 - trainer - INFO - Class distribution: Real=8, Fake=16
2025-07-27 00:05:52,597 - trainer - INFO - Applied feature scaling
2025-07-27 00:05:53,345 - trainer - INFO - Trained Random Forest classifier
2025-07-27 00:05:53,351 - trainer - INFO - Trained SVM classifier
2025-07-27 00:05:53,820 - trainer - INFO - Trained XGBoost classifier
2025-07-27 00:05:53,953 - trainer - INFO - random_forest Results:
2025-07-27 00:05:53,964 - trainer - INFO -   Accuracy: 0.4000
2025-07-27 00:05:53,992 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.33      0.50      0.40         2
        Fake       0.50      0.33      0.40         3

    accuracy                           0.40         5
   macro avg       0.42      0.42      0.40         5
weighted avg       0.43      0.40      0.40         5

2025-07-27 00:05:54,033 - trainer - INFO - svm Results:
2025-07-27 00:05:54,036 - trainer - INFO -   Accuracy: 0.6000
2025-07-27 00:05:54,081 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.00      0.00      0.00         2
        Fake       0.60      1.00      0.75         3

    accuracy                           0.60         5
   macro avg       0.30      0.50      0.38         5
weighted avg       0.36      0.60      0.45         5

2025-07-27 00:05:54,088 - trainer - INFO - xgboost Results:
2025-07-27 00:05:54,090 - trainer - INFO -   Accuracy: 0.0000
2025-07-27 00:05:54,115 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.00      0.00      0.00       2.0
        Fake       0.00      0.00      0.00       3.0

    accuracy                           0.00       5.0
   macro avg       0.00      0.00      0.00       5.0
weighted avg       0.00      0.00      0.00       5.0

2025-07-27 00:05:54,117 - trainer - INFO - Best individual model: svm (Accuracy: 0.6000)
2025-07-27 00:05:54,128 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm.pkl
2025-07-27 00:05:54,132 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm_scaler.pkl
2025-07-27 00:05:54,281 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-27 00:05:54,283 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-27 00:05:54,294 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\svm.pkl
2025-07-27 00:05:54,298 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\svm_scaler.pkl
2025-07-27 00:05:54,315 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\xgboost.pkl
2025-07-27 00:05:54,321 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\xgboost_scaler.pkl
2025-07-27 00:05:54,323 - trainer - INFO - Ensemble training completed
2025-07-27 00:08:44,515 - config - INFO - System initialized successfully
2025-07-27 00:08:44,520 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-27 00:09:35,800 - predictor - INFO - Initializing DeepfakePredictor with model: best_random_forest
2025-07-27 00:09:36,180 - predictor - INFO - Loaded fallback model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-27 00:09:36,182 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-27 00:09:36,933 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-27 00:09:36,934 - predictor - INFO - Analyzing video: image 1.mp4
2025-07-27 00:09:36,935 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-27 00:09:36,941 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-27 00:11:46,883 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:11:58,823 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:11:58,823 - extractor - INFO - Successfully extracted features from dataset/fake/image 1.mp4
2025-07-27 00:11:58,960 - predictor - INFO - Prediction: real (confidence: 0.6189)
2025-07-27 00:11:58,963 - utils - INFO - Operation 'prediction' took 143.17 seconds
2025-07-27 00:13:36,438 - config - INFO - System initialized successfully
2025-07-27 00:13:36,451 - predictor - INFO - Initializing DeepfakePredictor with model: best_svm
2025-07-27 00:13:36,574 - predictor - INFO - Loaded model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm.pkl
2025-07-27 00:13:36,578 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm_scaler.pkl
2025-07-27 00:13:37,350 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-27 00:16:43,439 - config - INFO - System initialized successfully
2025-07-27 00:16:43,447 - predictor - INFO - Initializing DeepfakePredictor with model: best_svm
2025-07-27 00:16:43,573 - predictor - INFO - Loaded model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm.pkl
2025-07-27 00:16:43,575 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm_scaler.pkl
2025-07-27 00:16:44,371 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-27 00:16:44,373 - predictor - INFO - Analyzing video: image 2 (21).mp4
2025-07-27 00:16:44,373 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-27 00:16:44,384 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-27 00:18:38,615 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:18:50,071 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:18:50,071 - extractor - INFO - Successfully extracted features from dataset/real/image 2 (21).mp4
2025-07-27 00:18:50,078 - predictor - INFO - Prediction: fake (confidence: 0.6694)
2025-07-27 00:18:50,079 - predictor - INFO - Analyzing video: image 1.mp4
2025-07-27 00:18:50,080 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-27 00:18:50,085 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-27 00:21:01,775 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:21:13,271 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:21:13,271 - extractor - INFO - Successfully extracted features from dataset/fake/image 1.mp4
2025-07-27 00:21:13,274 - predictor - INFO - Prediction: fake (confidence: 0.6694)
2025-07-27 00:21:13,275 - predictor - INFO - Analyzing video: image 2 (4).mp4
2025-07-27 00:21:13,276 - extractor - INFO - Starting feature extraction for image 2 (4).mp4
2025-07-27 00:21:13,282 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-27 00:23:33,149 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:23:45,765 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:23:45,766 - extractor - INFO - Successfully extracted features from dataset/real/image 2 (4).mp4
2025-07-27 00:23:45,768 - predictor - INFO - Prediction: fake (confidence: 0.6694)
2025-07-27 00:23:45,770 - predictor - INFO - Analyzing video: image 2 (8).mp4
2025-07-27 00:23:45,771 - extractor - INFO - Starting feature extraction for image 2 (8).mp4
2025-07-27 00:23:45,775 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-27 00:25:41,199 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:25:54,117 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:25:54,117 - extractor - INFO - Successfully extracted features from dataset/fake/image 2 (8).mp4
2025-07-27 00:25:54,119 - predictor - INFO - Prediction: fake (confidence: 0.6694)
2025-07-27 00:27:17,239 - config - INFO - System initialized successfully
2025-07-27 00:27:17,245 - trainer - INFO - Initialized FeatureLoader
2025-07-27 00:27:17,245 - trainer - INFO - Initialized ModelTrainer
2025-07-27 00:27:17,257 - trainer - INFO - Loaded 8 real samples
2025-07-27 00:27:17,275 - trainer - INFO - Loaded 16 fake samples
2025-07-27 00:27:17,275 - trainer - WARNING - Inconsistent feature shapes detected: {150528, 3072}
2025-07-27 00:27:17,276 - trainer - INFO - Normalized all features to shape: 3072
2025-07-27 00:27:17,276 - trainer - INFO - Final dataset: 24 samples, 3072 features
2025-07-27 00:27:17,277 - trainer - INFO - Class distribution: Real=8, Fake=16
2025-07-27 00:27:17,575 - trainer - INFO - Applied feature scaling
2025-07-27 00:27:17,926 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\balanced_rf.pkl
2025-07-27 00:27:17,931 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\balanced_rf_scaler.pkl
2025-07-27 00:28:28,415 - config - INFO - System initialized successfully
2025-07-27 00:28:28,422 - predictor - INFO - Initializing DeepfakePredictor with model: balanced_rf
2025-07-27 00:28:28,686 - predictor - INFO - Loaded model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\balanced_rf.pkl
2025-07-27 00:28:28,688 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\balanced_rf_scaler.pkl
2025-07-27 00:28:29,757 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-27 00:28:29,759 - predictor - INFO - Analyzing video: image 2 (21).mp4
2025-07-27 00:28:29,759 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-27 00:28:29,772 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-27 00:30:27,106 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:30:38,852 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:30:38,853 - extractor - INFO - Successfully extracted features from dataset/real/image 2 (21).mp4
2025-07-27 00:30:38,870 - predictor - INFO - Prediction: real (confidence: 0.6900)
2025-07-27 00:30:38,872 - predictor - INFO - Analyzing video: image 1.mp4
2025-07-27 00:30:38,873 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-27 00:30:38,882 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-27 00:32:48,765 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:33:02,296 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:33:02,297 - extractor - INFO - Successfully extracted features from dataset/fake/image 1.mp4
2025-07-27 00:33:02,323 - predictor - INFO - Prediction: real (confidence: 0.6500)
2025-07-27 00:33:02,324 - predictor - INFO - Analyzing video: image 2 (4).mp4
2025-07-27 00:33:02,325 - extractor - INFO - Starting feature extraction for image 2 (4).mp4
2025-07-27 00:33:02,332 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-27 00:35:10,595 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:35:22,213 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:35:22,213 - extractor - INFO - Successfully extracted features from dataset/real/image 2 (4).mp4
2025-07-27 00:35:22,235 - predictor - INFO - Prediction: real (confidence: 0.6400)
2025-07-27 00:35:22,236 - predictor - INFO - Analyzing video: image 2 (8).mp4
2025-07-27 00:35:22,240 - extractor - INFO - Starting feature extraction for image 2 (8).mp4
2025-07-27 00:35:22,246 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-27 00:37:19,881 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 00:37:31,509 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 00:37:31,510 - extractor - INFO - Successfully extracted features from dataset/fake/image 2 (8).mp4
2025-07-27 00:37:31,526 - predictor - INFO - Prediction: fake (confidence: 0.6000)
2025-07-27 15:24:20,122 - config - INFO - System initialized successfully
2025-07-27 15:24:20,130 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-27 15:26:00,244 - config - INFO - System initialized successfully
2025-07-27 15:26:00,266 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-27 15:27:13,285 - extractor - INFO - Starting batch feature extraction for real videos
2025-07-27 15:27:14,420 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-27 15:27:14,421 - extractor - INFO - Starting feature extraction for image 2 (101).mp4
2025-07-27 15:27:14,655 - extractor - INFO - Processing video: 373 frames, 30.0 FPS
2025-07-27 15:28:59,920 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 15:29:11,315 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 15:29:11,316 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (101).mp4
2025-07-27 15:29:11,318 - extractor - INFO - Saved features: image_2_(101)_real.npy
2025-07-27 15:29:11,319 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-27 15:29:11,339 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-27 15:31:03,610 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 15:31:14,566 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 15:31:14,567 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (21).mp4
2025-07-27 15:31:14,570 - extractor - INFO - Saved features: image_2_(21)_real.npy
2025-07-27 15:31:14,570 - extractor - INFO - Starting feature extraction for image 2 (4).mp4
2025-07-27 15:31:14,597 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-27 15:33:28,395 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 15:33:45,288 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 15:33:45,289 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (4).mp4
2025-07-27 15:33:45,292 - extractor - INFO - Saved features: image_2_(4)_real.npy
2025-07-27 15:33:45,293 - extractor - INFO - Starting feature extraction for image 2 (61).mp4
2025-07-27 15:33:45,322 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-27 15:36:39,237 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 15:36:55,990 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 15:36:55,990 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (61).mp4
2025-07-27 15:36:55,993 - extractor - INFO - Saved features: image_2_(61)_real.npy
2025-07-27 15:36:55,995 - extractor - INFO - Batch extraction completed: 4 videos processed
2025-07-27 15:36:55,998 - extractor - INFO - Starting batch feature extraction for fake videos
2025-07-27 15:36:57,000 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-27 15:36:57,001 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-27 15:36:57,043 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-27 15:39:06,390 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 15:39:22,566 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 15:39:22,567 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 1.mp4
2025-07-27 15:39:22,571 - extractor - INFO - Saved features: image_1_fake.npy
2025-07-27 15:39:22,572 - extractor - INFO - Starting feature extraction for image 2 (10).mp4
2025-07-27 15:39:22,670 - extractor - INFO - Processing video: 349 frames, 30.0 FPS
2025-07-27 15:41:37,944 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 15:41:54,865 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 15:41:54,866 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (10).mp4
2025-07-27 15:41:54,869 - extractor - INFO - Saved features: image_2_(10)_fake.npy
2025-07-27 15:41:54,869 - extractor - INFO - Starting feature extraction for image 2 (8).mp4
2025-07-27 15:41:54,899 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-27 15:44:56,718 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 15:45:12,717 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 15:45:12,717 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (8).mp4
2025-07-27 15:45:12,720 - extractor - INFO - Saved features: image_2_(8)_fake.npy
2025-07-27 15:45:12,721 - extractor - INFO - Starting feature extraction for image 2 (9).mp4
2025-07-27 15:45:12,746 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-27 15:47:52,701 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-27 15:48:08,568 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-27 15:48:08,569 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (9).mp4
2025-07-27 15:48:08,572 - extractor - INFO - Saved features: image_2_(9)_fake.npy
2025-07-27 15:48:08,573 - extractor - INFO - Batch extraction completed: 4 videos processed
2025-07-27 15:48:08,575 - utils - INFO - Operation 'feature_extraction' took 1255.29 seconds
2025-07-29 15:00:23,874 - config - INFO - System initialized successfully
2025-07-29 15:00:23,885 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-29 15:00:29,502 - extractor - INFO - Starting batch feature extraction for real videos
2025-07-29 15:00:30,593 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-29 15:00:30,594 - extractor - INFO - Starting feature extraction for image 2 (101).mp4
2025-07-29 15:00:30,805 - extractor - INFO - Processing video: 373 frames, 30.0 FPS
2025-07-29 15:02:52,849 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:03:13,499 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:03:13,499 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (101).mp4
2025-07-29 15:03:13,505 - extractor - INFO - Saved features: image_2_(101)_real.npy
2025-07-29 15:03:13,505 - extractor - INFO - Starting feature extraction for image 2 (21).mp4
2025-07-29 15:03:13,539 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-29 15:05:15,842 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:05:26,948 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:05:26,949 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (21).mp4
2025-07-29 15:05:26,951 - extractor - INFO - Saved features: image_2_(21)_real.npy
2025-07-29 15:05:26,952 - extractor - INFO - Starting feature extraction for image 2 (4).mp4
2025-07-29 15:05:27,001 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-29 15:07:50,071 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:08:02,238 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:08:02,238 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (4).mp4
2025-07-29 15:08:02,240 - extractor - INFO - Saved features: image_2_(4)_real.npy
2025-07-29 15:08:02,241 - extractor - INFO - Starting feature extraction for image 2 (61).mp4
2025-07-29 15:08:02,244 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-29 15:10:21,961 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:10:34,530 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:10:34,531 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 2 (61).mp4
2025-07-29 15:10:34,533 - extractor - INFO - Saved features: image_2_(61)_real.npy
2025-07-29 15:10:34,534 - extractor - INFO - Batch extraction completed: 4 videos processed
2025-07-29 15:10:34,538 - extractor - INFO - Starting batch feature extraction for fake videos
2025-07-29 15:10:35,662 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-29 15:10:35,663 - extractor - INFO - Starting feature extraction for image 1.mp4
2025-07-29 15:10:35,669 - extractor - INFO - Processing video: 529 frames, 30.0 FPS
2025-07-29 15:12:43,592 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:12:55,549 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:12:55,549 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 1.mp4
2025-07-29 15:12:55,552 - extractor - INFO - Saved features: image_1_fake.npy
2025-07-29 15:12:55,552 - extractor - INFO - Starting feature extraction for image 2 (10).mp4
2025-07-29 15:12:55,556 - extractor - INFO - Processing video: 349 frames, 30.0 FPS
2025-07-29 15:14:22,477 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:14:33,043 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:14:33,043 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (10).mp4
2025-07-29 15:14:33,046 - extractor - INFO - Saved features: image_2_(10)_fake.npy
2025-07-29 15:14:33,046 - extractor - INFO - Starting feature extraction for image 2 (8).mp4
2025-07-29 15:14:33,050 - extractor - INFO - Processing video: 445 frames, 30.0 FPS
2025-07-29 15:16:22,402 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:16:33,256 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:16:33,257 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (8).mp4
2025-07-29 15:16:33,259 - extractor - INFO - Saved features: image_2_(8)_fake.npy
2025-07-29 15:16:33,259 - extractor - INFO - Starting feature extraction for image 2 (9).mp4
2025-07-29 15:16:33,264 - extractor - INFO - Processing video: 280 frames, 30.0 FPS
2025-07-29 15:18:50,517 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:19:00,891 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:19:00,892 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (9).mp4
2025-07-29 15:19:00,894 - extractor - INFO - Saved features: image_2_(9)_fake.npy
2025-07-29 15:19:00,895 - extractor - INFO - Batch extraction completed: 4 videos processed
2025-07-29 15:19:00,897 - utils - INFO - Operation 'feature_extraction' took 1111.39 seconds
2025-07-29 15:19:26,771 - trainer - INFO - Initialized FeatureLoader
2025-07-29 15:19:26,772 - trainer - INFO - Initialized ModelTrainer
2025-07-29 15:19:26,773 - trainer - INFO - Starting model training: ensemble
2025-07-29 15:19:26,794 - trainer - INFO - Loaded 8 real samples
2025-07-29 15:19:26,838 - trainer - INFO - Loaded 16 fake samples
2025-07-29 15:19:26,838 - trainer - WARNING - Inconsistent feature shapes detected: {150528, 3072}
2025-07-29 15:19:26,839 - trainer - INFO - Normalized all features to shape: 3072
2025-07-29 15:19:26,839 - trainer - INFO - Final dataset: 24 samples, 3072 features
2025-07-29 15:19:26,842 - trainer - INFO - Class distribution: Real=8, Fake=16
2025-07-29 15:19:26,972 - trainer - INFO - Applied feature scaling
2025-07-29 15:19:29,206 - trainer - INFO - Trained Random Forest classifier
2025-07-29 15:19:29,217 - trainer - INFO - Trained SVM classifier
2025-07-29 15:19:29,759 - trainer - INFO - Trained XGBoost classifier
2025-07-29 15:19:29,841 - trainer - INFO - random_forest Results:
2025-07-29 15:19:29,842 - trainer - INFO -   Accuracy: 0.4000
2025-07-29 15:19:29,863 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.33      0.50      0.40         2
        Fake       0.50      0.33      0.40         3

    accuracy                           0.40         5
   macro avg       0.42      0.42      0.40         5
weighted avg       0.43      0.40      0.40         5

2025-07-29 15:19:29,866 - trainer - INFO - svm Results:
2025-07-29 15:19:29,866 - trainer - INFO -   Accuracy: 0.6000
2025-07-29 15:19:29,952 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.00      0.00      0.00         2
        Fake       0.60      1.00      0.75         3

    accuracy                           0.60         5
   macro avg       0.30      0.50      0.38         5
weighted avg       0.36      0.60      0.45         5

2025-07-29 15:19:29,965 - trainer - INFO - xgboost Results:
2025-07-29 15:19:29,965 - trainer - INFO -   Accuracy: 0.2000
2025-07-29 15:19:29,991 - trainer - INFO -   Classification Report:
              precision    recall  f1-score   support

        Real       0.00      0.00      0.00         2
        Fake       0.33      0.33      0.33         3

    accuracy                           0.20         5
   macro avg       0.17      0.17      0.17         5
weighted avg       0.20      0.20      0.20         5

2025-07-29 15:19:29,992 - trainer - INFO - Best individual model: svm (Accuracy: 0.6000)
2025-07-29 15:19:30,000 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm.pkl
2025-07-29 15:19:30,007 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\best_svm_scaler.pkl
2025-07-29 15:19:30,170 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-29 15:19:30,172 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-29 15:19:30,177 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\svm.pkl
2025-07-29 15:19:30,180 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\svm_scaler.pkl
2025-07-29 15:19:30,189 - trainer - INFO - Saved model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\xgboost.pkl
2025-07-29 15:19:30,193 - trainer - INFO - Saved scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\xgboost_scaler.pkl
2025-07-29 15:19:30,194 - trainer - INFO - Ensemble training completed
2025-07-29 15:19:30,196 - utils - INFO - Operation 'model_training' took 3.42 seconds
2025-07-29 15:19:43,804 - config - INFO - System initialized successfully
2025-07-29 15:19:43,855 - predictor - INFO - Initializing DeepfakePredictor with model: best_random_forest
2025-07-29 15:19:43,993 - predictor - INFO - Loaded fallback model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-29 15:19:43,995 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-29 15:19:44,732 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-29 15:26:32,643 - predictor - INFO - Initializing DeepfakePredictor with model: best_random_forest
2025-07-29 15:26:32,746 - predictor - INFO - Loaded fallback model: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest.pkl
2025-07-29 15:26:32,748 - predictor - INFO - Loaded scaler: C:\Users\<USER>\Desktop\Project 1\deepfake_detector\models\random_forest_scaler.pkl
2025-07-29 15:26:33,496 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-29 15:26:33,497 - predictor - INFO - Analyzing video: image 1 (102).mp4
2025-07-29 15:26:33,497 - extractor - INFO - Starting feature extraction for image 1 (102).mp4
2025-07-29 15:26:33,500 - extractor - INFO - Processing video: 422 frames, 30.0 FPS
2025-07-29 15:28:06,722 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 15:28:17,938 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 15:28:17,939 - extractor - INFO - Successfully extracted features from F:/DatasetD/Celeb-synthesis/image 1 (102).mp4
2025-07-29 15:28:18,082 - predictor - INFO - Prediction: uncertain (confidence: 0.5950)
2025-07-29 20:54:51,134 - config - INFO - System initialized successfully
2025-07-29 20:54:51,141 - __main__ - INFO - Initialized DeepfakeDetectionPipeline
2025-07-29 20:55:01,051 - extractor - INFO - Starting batch feature extraction for real videos
2025-07-29 20:55:02,126 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-29 20:55:02,127 - extractor - INFO - Starting feature extraction for image 1 (163).mp4
2025-07-29 20:55:02,222 - extractor - INFO - Processing video: 376 frames, 30.0 FPS
2025-07-29 20:57:06,998 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 20:57:18,658 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 20:57:18,658 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 1 (163).mp4
2025-07-29 20:57:18,662 - extractor - INFO - Saved features: image_1_(163)_real.npy
2025-07-29 20:57:18,663 - extractor - INFO - Starting feature extraction for image 1 (522).mp4
2025-07-29 20:57:18,668 - extractor - INFO - Processing video: 401 frames, 30.0 FPS
2025-07-29 20:59:26,332 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 20:59:36,752 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 20:59:36,753 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 1 (522).mp4
2025-07-29 20:59:36,755 - extractor - INFO - Saved features: image_1_(522)_real.npy
2025-07-29 20:59:36,756 - extractor - INFO - Starting feature extraction for image 1 (634).mp4
2025-07-29 20:59:36,759 - extractor - INFO - Processing video: 352 frames, 30.0 FPS
2025-07-29 21:01:42,821 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:02:01,285 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:02:01,288 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 1 (634).mp4
2025-07-29 21:02:01,293 - extractor - INFO - Saved features: image_1_(634)_real.npy
2025-07-29 21:02:01,295 - extractor - INFO - Starting feature extraction for image 1 (689).mp4
2025-07-29 21:02:01,303 - extractor - INFO - Processing video: 315 frames, 30.0 FPS
2025-07-29 21:04:51,446 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:05:01,658 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:05:01,659 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 1 (689).mp4
2025-07-29 21:05:01,668 - extractor - INFO - Saved features: image_1_(689)_real.npy
2025-07-29 21:05:01,668 - extractor - INFO - Starting feature extraction for image 1 (84).mp4
2025-07-29 21:05:01,705 - extractor - INFO - Processing video: 422 frames, 30.0 FPS
2025-07-29 21:06:53,631 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:07:05,816 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:07:05,817 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\real\image 1 (84).mp4
2025-07-29 21:07:05,819 - extractor - INFO - Saved features: image_1_(84)_real.npy
2025-07-29 21:07:05,820 - extractor - INFO - Batch extraction completed: 5 videos processed
2025-07-29 21:07:05,822 - extractor - INFO - Starting batch feature extraction for fake videos
2025-07-29 21:07:06,716 - extractor - INFO - Initialized ResNeXtFeatureExtractor with MTCNN face detection
2025-07-29 21:07:06,717 - extractor - INFO - Starting feature extraction for image 2 (124).mp4
2025-07-29 21:07:06,722 - extractor - INFO - Processing video: 309 frames, 30.0 FPS
2025-07-29 21:09:04,115 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:09:20,009 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:09:20,010 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (124).mp4
2025-07-29 21:09:20,013 - extractor - INFO - Saved features: image_2_(124)_fake.npy
2025-07-29 21:09:20,014 - extractor - INFO - Starting feature extraction for image 2 (125).mp4
2025-07-29 21:09:20,021 - extractor - INFO - Processing video: 333 frames, 30.0 FPS
2025-07-29 21:11:31,092 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:11:41,013 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:11:41,013 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (125).mp4
2025-07-29 21:11:41,016 - extractor - INFO - Saved features: image_2_(125)_fake.npy
2025-07-29 21:11:41,017 - extractor - INFO - Starting feature extraction for image 2 (126).mp4
2025-07-29 21:11:41,022 - extractor - INFO - Processing video: 452 frames, 29.9 FPS
2025-07-29 21:13:39,210 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:13:49,538 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:13:49,539 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (126).mp4
2025-07-29 21:13:49,541 - extractor - INFO - Saved features: image_2_(126)_fake.npy
2025-07-29 21:13:49,542 - extractor - INFO - Starting feature extraction for image 2 (127).mp4
2025-07-29 21:13:49,547 - extractor - INFO - Processing video: 459 frames, 30.0 FPS
2025-07-29 21:15:45,775 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:15:56,101 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:15:56,101 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (127).mp4
2025-07-29 21:15:56,104 - extractor - INFO - Saved features: image_2_(127)_fake.npy
2025-07-29 21:15:56,105 - extractor - INFO - Starting feature extraction for image 2 (135).mp4
2025-07-29 21:15:56,109 - extractor - INFO - Processing video: 317 frames, 30.0 FPS
2025-07-29 21:17:50,881 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:18:02,867 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:18:02,868 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (135).mp4
2025-07-29 21:18:02,870 - extractor - INFO - Saved features: image_2_(135)_fake.npy
2025-07-29 21:18:02,871 - extractor - INFO - Starting feature extraction for image 2 (136).mp4
2025-07-29 21:18:02,876 - extractor - INFO - Processing video: 316 frames, 30.0 FPS
2025-07-29 21:20:00,977 - extractor - INFO - Extracted 50 faces, success rate: 102.0%
2025-07-29 21:20:14,374 - extractor - INFO - Extracted 3072 ResNeXt features from 50 faces
2025-07-29 21:20:14,375 - extractor - INFO - Successfully extracted features from C:\Users\<USER>\Desktop\Project 1\deepfake_detector\dataset\fake\image 2 (136).mp4
2025-07-29 21:20:14,377 - extractor - INFO - Saved features: image_2_(136)_fake.npy
2025-07-29 21:20:14,378 - extractor - INFO - Batch extraction completed: 6 videos processed
2025-07-29 21:20:14,380 - utils - INFO - Operation 'feature_extraction' took 1513.33 seconds

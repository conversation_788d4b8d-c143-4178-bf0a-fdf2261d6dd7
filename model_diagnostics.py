#!/usr/bin/env python3
"""
Model diagnostics script to identify detection issues
"""

import os
import numpy as np
import logging
from pathlib import Path

from config import initialize_system, DATASET_CONFIG, MODELS_PATH
from predictor import DeepfakePredictor, predict_video
from trainer import ModelTrainer
from extractor import get_feature_extractor

logger = logging.getLogger(__name__)

def test_feature_extraction():
    """Test feature extraction on sample videos"""
    print("\n🔍 TESTING FEATURE EXTRACTION")
    print("="*50)
    
    extractor = get_feature_extractor()
    print(f"Using extractor: {type(extractor).__name__}")
    
    # Test on real videos
    real_path = DATASET_CONFIG["real_videos_path"]
    fake_path = DATASET_CONFIG["fake_videos_path"]
    
    if os.path.exists(real_path):
        real_videos = [f for f in os.listdir(real_path) if f.endswith('.mp4')][:2]
        print(f"\nTesting on {len(real_videos)} real videos:")
        
        for video in real_videos:
            video_path = os.path.join(real_path, video)
            features = extractor.extract_features_from_video(video_path)
            print(f"  {video}: {features.shape if features.size > 0 else 'No features'}")
    
    if os.path.exists(fake_path):
        fake_videos = [f for f in os.listdir(fake_path) if f.endswith('.mp4')][:2]
        print(f"\nTesting on {len(fake_videos)} fake videos:")
        
        for video in fake_videos:
            video_path = os.path.join(fake_path, video)
            features = extractor.extract_features_from_video(video_path)
            print(f"  {video}: {features.shape if features.size > 0 else 'No features'}")

def test_model_predictions():
    """Test model predictions on known videos"""
    print("\n🎯 TESTING MODEL PREDICTIONS")
    print("="*50)
    
    # Find available models
    model_files = [f for f in os.listdir(MODELS_PATH) if f.endswith('.pkl') and 'scaler' not in f]
    print(f"Available models: {model_files}")
    
    if not model_files:
        print("❌ No trained models found!")
        return
    
    # Test with different models
    for model_file in model_files[:3]:  # Test first 3 models
        model_name = model_file.replace('.pkl', '')
        print(f"\n--- Testing {model_name} ---")
        
        try:
            predictor = DeepfakePredictor(model_name)
            
            # Test on real videos
            real_path = DATASET_CONFIG["real_videos_path"]
            if os.path.exists(real_path):
                real_videos = [f for f in os.listdir(real_path) if f.endswith('.mp4')][:2]
                
                for video in real_videos:
                    video_path = os.path.join(real_path, video)
                    result = predictor.predict(video_path)
                    print(f"  REAL {video}: {result['prediction']} (conf: {result['confidence']:.3f})")
            
            # Test on fake videos
            fake_path = DATASET_CONFIG["fake_videos_path"]
            if os.path.exists(fake_path):
                fake_videos = [f for f in os.listdir(fake_path) if f.endswith('.mp4')][:2]
                
                for video in fake_videos:
                    video_path = os.path.join(fake_path, video)
                    result = predictor.predict(video_path)
                    print(f"  FAKE {video}: {result['prediction']} (conf: {result['confidence']:.3f})")
                    
        except Exception as e:
            print(f"  ❌ Error with {model_name}: {e}")

def analyze_training_data():
    """Analyze the training data quality"""
    print("\n📊 ANALYZING TRAINING DATA")
    print("="*50)
    
    trainer = ModelTrainer()
    X, y, filenames = trainer.feature_loader.load_features()
    
    if X is None:
        print("❌ No features loaded!")
        return
    
    print(f"Total samples: {len(X)}")
    print(f"Feature dimension: {X.shape[1] if len(X.shape) > 1 else 'Unknown'}")
    print(f"Real samples: {np.sum(y == 0)}")
    print(f"Fake samples: {np.sum(y == 1)}")
    
    # Check for data issues
    if len(X) < 10:
        print("⚠️  WARNING: Very few training samples!")
    
    if np.sum(y == 0) == 0:
        print("❌ ERROR: No real samples!")
    
    if np.sum(y == 1) == 0:
        print("❌ ERROR: No fake samples!")
    
    # Check feature quality
    if len(X.shape) > 1:
        zero_features = np.sum(np.all(X == 0, axis=0))
        constant_features = np.sum(np.var(X, axis=0) < 1e-8)
        
        print(f"Zero features: {zero_features}")
        print(f"Constant features: {constant_features}")
        
        if zero_features > X.shape[1] * 0.5:
            print("⚠️  WARNING: Many zero features detected!")
        
        if constant_features > X.shape[1] * 0.3:
            print("⚠️  WARNING: Many constant features detected!")

def check_model_performance():
    """Check model performance metrics"""
    print("\n📈 MODEL PERFORMANCE CHECK")
    print("="*50)
    
    # Look for confusion matrix or performance logs
    confusion_matrix_path = os.path.join(MODELS_PATH, "confusion_matrix.png")
    if os.path.exists(confusion_matrix_path):
        print("✅ Confusion matrix found")
    else:
        print("❌ No confusion matrix found")
    
    # Check log files for performance metrics
    log_files = [f for f in os.listdir('.') if f.endswith('.log')]
    if log_files:
        print(f"Log files found: {log_files}")
        # Could parse logs for accuracy metrics here
    else:
        print("No log files found")

def suggest_improvements():
    """Suggest improvements based on diagnostics"""
    print("\n💡 IMPROVEMENT SUGGESTIONS")
    print("="*50)
    
    print("1. 📹 Video Quality:")
    print("   - Ensure videos have clear faces")
    print("   - Check video resolution (minimum 480p recommended)")
    print("   - Verify good lighting conditions")
    
    print("\n2. 🎯 Training Data:")
    print("   - Add more diverse training samples")
    print("   - Balance real vs fake samples")
    print("   - Include different types of deepfakes")
    
    print("\n3. 🔧 Model Configuration:")
    print("   - Try different feature extractors")
    print("   - Adjust confidence thresholds")
    print("   - Use ensemble models for better accuracy")
    
    print("\n4. ⚙️  Feature Extraction:")
    print("   - Increase frame sampling rate")
    print("   - Enable face enhancement")
    print("   - Check face detection success rate")

def main():
    """Run comprehensive model diagnostics"""
    print("🔍 DEEPFAKE MODEL DIAGNOSTICS")
    print("="*60)
    
    if not initialize_system():
        print("❌ System initialization failed!")
        return
    
    test_feature_extraction()
    analyze_training_data()
    test_model_predictions()
    check_model_performance()
    suggest_improvements()
    
    print("\n✅ Diagnostics completed!")

if __name__ == "__main__":
    main()

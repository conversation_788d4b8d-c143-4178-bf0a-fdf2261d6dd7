"""
Feature loading, model training, and model saving module
"""

import os
import numpy as np
import logging
from pathlib import Path
from config import TRAINING_CONFIG, FEATURES_PATH, MODELS_PATH, AVAILABLE_FEATURES, get_model_path

logger = logging.getLogger(__name__)

class FeatureLoader:
    """Handles loading and preprocessing of extracted features"""
    
    def __init__(self):
        self.features_path = FEATURES_PATH
        logger.info("Initialized FeatureLoader")
    
    def load_features(self):
        """Load all features from the features directory"""
        X, y, filenames = [], [], []
        feature_shapes = []
        
        # Load real features (label = 0)
        real_path = os.path.join(self.features_path, "real")
        if os.path.exists(real_path):
            real_count = self._load_features_from_directory(real_path, 0, X, y, filenames, feature_shapes)
            logger.info(f"Loaded {real_count} real samples")
        
        # Load fake features (label = 1)
        fake_path = os.path.join(self.features_path, "fake")
        if os.path.exists(fake_path):
            fake_count = self._load_features_from_directory(fake_path, 1, X, y, filenames, feature_shapes)
            logger.info(f"Loaded {fake_count} fake samples")
        
        if len(X) == 0:
            logger.error("No features found! Run feature extraction first.")
            return None, None, None
        
        # Handle shape inconsistencies
        X = self._normalize_feature_shapes(X, feature_shapes)
        
        X_array = np.array(X)
        y_array = np.array(y)
        
        logger.info(f"Final dataset: {X_array.shape[0]} samples, {X_array.shape[1]} features")
        logger.info(f"Class distribution: Real={np.sum(y_array==0)}, Fake={np.sum(y_array==1)}")
        
        return X_array, y_array, filenames
    
    def _load_features_from_directory(self, directory, label, X, y, filenames, feature_shapes):
        """Load features from a specific directory"""
        count = 0
        
        for filename in os.listdir(directory):
            if filename.endswith('.npy'):
                filepath = os.path.join(directory, filename)
                try:
                    features = np.load(filepath)
                    
                    # Flatten if multi-dimensional
                    if features.ndim > 1:
                        features = features.flatten()
                    
                    X.append(features)
                    y.append(label)
                    filenames.append(filename)
                    feature_shapes.append(features.shape[0])
                    count += 1
                    
                except Exception as e:
                    logger.error(f"Error loading {filepath}: {e}")
        
        return count
    
    def _normalize_feature_shapes(self, X, feature_shapes):
        """Normalize features to have consistent shapes"""
        if len(set(feature_shapes)) > 1:
            logger.warning(f"Inconsistent feature shapes detected: {set(feature_shapes)}")
            
            min_shape = min(feature_shapes)
            normalized_X = []
            
            for features in X:
                if len(features) > min_shape:
                    normalized_features = features[:min_shape]
                elif len(features) < min_shape:
                    normalized_features = np.pad(features, (0, min_shape - len(features)), 'constant')
                else:
                    normalized_features = features
                
                normalized_X.append(normalized_features)
            
            logger.info(f"Normalized all features to shape: {min_shape}")
            return normalized_X
        
        return X

class ModelTrainer:
    """Handles model training with multiple algorithms"""
    
    def __init__(self):
        self.models_path = MODELS_PATH
        self.feature_loader = FeatureLoader()
        logger.info("Initialized ModelTrainer")
    
    def prepare_data(self, X, y):
        """Prepare data for training with scaling and splitting"""
        from sklearn.model_selection import train_test_split
        from sklearn.preprocessing import StandardScaler
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y,
            test_size=TRAINING_CONFIG["test_size"],
            random_state=TRAINING_CONFIG["random_state"],
            stratify=y if TRAINING_CONFIG["stratify"] else None
        )
        
        # Scale features if enabled
        scaler = None
        if TRAINING_CONFIG["feature_scaling"]:
            scaler = StandardScaler()
            X_train = scaler.fit_transform(X_train)
            X_test = scaler.transform(X_test)
            logger.info("Applied feature scaling")
        
        # Apply PCA if enabled
        pca = None
        if TRAINING_CONFIG.get("apply_pca", False):
            try:
                from sklearn.decomposition import PCA
                n_components = min(TRAINING_CONFIG.get("pca_components", 512), X_train.shape[1])
                pca = PCA(n_components=n_components)
                X_train = pca.fit_transform(X_train)
                X_test = pca.transform(X_test)
                logger.info(f"Applied PCA: {X_train.shape[1]} components")
            except ImportError:
                logger.warning("PCA not available")
        
        return X_train, X_test, y_train, y_test, scaler, pca
    
    def train_random_forest(self, X_train, y_train):
        """Train Random Forest classifier"""
        from sklearn.ensemble import RandomForestClassifier
        
        rf = RandomForestClassifier(
            n_estimators=TRAINING_CONFIG["rf_n_estimators"],
            max_depth=TRAINING_CONFIG["rf_max_depth"],
            min_samples_split=TRAINING_CONFIG["rf_min_samples_split"],
            min_samples_leaf=TRAINING_CONFIG["rf_min_samples_leaf"],
            random_state=TRAINING_CONFIG["random_state"],
            n_jobs=-1
        )
        
        rf.fit(X_train, y_train)
        logger.info("Trained Random Forest classifier")
        return rf
    
    def train_svm(self, X_train, y_train):
        """Train SVM classifier"""
        try:
            from sklearn.svm import SVC
            
            svm = SVC(
                kernel=TRAINING_CONFIG["svm_kernel"],
                C=TRAINING_CONFIG["svm_C"],
                gamma=TRAINING_CONFIG["svm_gamma"],
                probability=True,
                random_state=TRAINING_CONFIG["random_state"]
            )
            
            svm.fit(X_train, y_train)
            logger.info("Trained SVM classifier")
            return svm
        except ImportError:
            logger.warning("SVM not available")
            return None
    
    def train_xgboost(self, X_train, y_train):
        """Train XGBoost classifier"""
        try:
            import xgboost as xgb
            
            xgb_model = xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                random_state=TRAINING_CONFIG["random_state"]
            )
            
            xgb_model.fit(X_train, y_train)
            logger.info("Trained XGBoost classifier")
            return xgb_model
        except ImportError:
            logger.warning("XGBoost not available")
            return None
    
    def train_neural_network(self, X_train, y_train):
        """Train Neural Network classifier"""
        try:
            from sklearn.neural_network import MLPClassifier
            
            nn = MLPClassifier(
                hidden_layer_sizes=tuple(TRAINING_CONFIG["nn_hidden_layers"]),
                activation=TRAINING_CONFIG["nn_activation"],
                max_iter=TRAINING_CONFIG["nn_max_iter"],
                random_state=TRAINING_CONFIG["random_state"]
            )
            
            nn.fit(X_train, y_train)
            logger.info("Trained Neural Network classifier")
            return nn
        except ImportError:
            logger.warning("Neural Network not available")
            return None
    
    def evaluate_model(self, model, X_test, y_test, model_name):
        """Evaluate a trained model"""
        from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
        
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        logger.info(f"{model_name} Results:")
        logger.info(f"  Accuracy: {accuracy:.4f}")
        
        # Detailed classification report
        report = classification_report(y_test, y_pred, target_names=['Real', 'Fake'])
        logger.info(f"  Classification Report:\n{report}")
        
        return accuracy, y_pred
    
    def train_ensemble(self, X_train, y_train, X_test, y_test):
        """Train ensemble of multiple models"""
        models = {}
        
        # Train individual models
        if "random_forest" in TRAINING_CONFIG["ensemble_models"]:
            models["random_forest"] = self.train_random_forest(X_train, y_train)
        
        if "svm" in TRAINING_CONFIG["ensemble_models"]:
            svm_model = self.train_svm(X_train, y_train)
            if svm_model:
                models["svm"] = svm_model
        
        if "xgboost" in TRAINING_CONFIG["ensemble_models"] and AVAILABLE_FEATURES["ensemble_models"]:
            xgb_model = self.train_xgboost(X_train, y_train)
            if xgb_model:
                models["xgboost"] = xgb_model
        
        if "neural_network" in TRAINING_CONFIG["ensemble_models"]:
            nn_model = self.train_neural_network(X_train, y_train)
            if nn_model:
                models["neural_network"] = nn_model
        
        # Evaluate individual models
        best_model = None
        best_score = 0
        best_name = ""
        
        for name, model in models.items():
            accuracy, _ = self.evaluate_model(model, X_test, y_test, name)
            if accuracy > best_score:
                best_score = accuracy
                best_model = model
                best_name = name
        
        logger.info(f"Best individual model: {best_name} (Accuracy: {best_score:.4f})")
        
        return models, best_model, best_name
    
    def save_model(self, model, scaler, model_name, pca=None):
        """Save trained model and preprocessing components"""
        try:
            import joblib
            
            os.makedirs(self.models_path, exist_ok=True)
            
            # Save model
            model_path = get_model_path(model_name)
            joblib.dump(model, model_path)
            logger.info(f"Saved model: {model_path}")
            
            # Save scaler
            if scaler:
                scaler_path = get_model_path(f"{model_name}_scaler")
                joblib.dump(scaler, scaler_path)
                logger.info(f"Saved scaler: {scaler_path}")
            
            # Save PCA
            if pca:
                pca_path = get_model_path(f"{model_name}_pca")
                joblib.dump(pca, pca_path)
                logger.info(f"Saved PCA: {pca_path}")
            
            return True
            
        except ImportError:
            logger.error("joblib not available - cannot save model")
            return False
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return False
    
    def train_model(self, model_type="auto"):
        """Main training function"""
        logger.info(f"Starting model training: {model_type}")
        
        # Load features
        X, y, filenames = self.feature_loader.load_features()
        if X is None:
            logger.error("No features available for training")
            return False
        
        # Check minimum data requirements
        if len(X) < 4:
            logger.error("Insufficient data for training (need at least 4 samples)")
            return False
        
        # Prepare data
        X_train, X_test, y_train, y_test, scaler, pca = self.prepare_data(X, y)
        
        # Train model(s)
        if model_type == "auto" or model_type == "ensemble":
            if TRAINING_CONFIG["enable_ensemble"]:
                models, best_model, best_name = self.train_ensemble(X_train, y_train, X_test, y_test)
                
                # Save best model
                self.save_model(best_model, scaler, f"best_{best_name}", pca)
                
                # Save ensemble models
                for name, model in models.items():
                    self.save_model(model, scaler, name, pca)
                
                logger.info("Ensemble training completed")
                return True
        
        # Train single model
        model_name = model_type if model_type != "auto" else TRAINING_CONFIG["default_classifier"]
        
        if model_name == "random_forest":
            model = self.train_random_forest(X_train, y_train)
        elif model_name == "svm":
            model = self.train_svm(X_train, y_train)
        elif model_name == "xgboost":
            model = self.train_xgboost(X_train, y_train)
        elif model_name == "neural_network":
            model = self.train_neural_network(X_train, y_train)
        else:
            logger.error(f"Unknown model type: {model_name}")
            return False
        
        if model is None:
            logger.error(f"Failed to train {model_name}")
            return False
        
        # Evaluate model
        self.evaluate_model(model, X_test, y_test, model_name)
        
        # Save model
        self.save_model(model, scaler, model_name, pca)
        
        logger.info(f"Training completed: {model_name}")
        return True

def train_model(model_type="auto"):
    """Convenience function for training models"""
    trainer = ModelTrainer()
    return trainer.train_model(model_type)

if __name__ == "__main__":
    # Test training
    from config import initialize_system
    
    if initialize_system():
        success = train_model("random_forest")
        if success:
            print("✅ Model training completed")
        else:
            print("❌ Model training failed")
    else:
        print("❌ Failed to initialize system")

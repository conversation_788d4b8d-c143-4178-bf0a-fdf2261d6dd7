#!/usr/bin/env python3
"""
Main entry point for Deepfake Detection System
Orchestrates the full pipeline: feature extraction, training, and prediction
"""

import os
import sys
import logging
from pathlib import Path

# Import our modules
from config import initialize_system, DATASET_CONFIG, AVAILABLE_FEATURES, DATASET_PATH
from extractor import batch_extract_features, get_feature_extractor
from trainer import train_model
from predictor import predict_video, predict_batch, DeepfakePredictor
from utils import DatasetValidator, PerformanceMonitor, log_system_info

logger = logging.getLogger(__name__)

class DeepfakeDetectionPipeline:
    """Main pipeline orchestrator for deepfake detection"""
    
    def __init__(self):
        self.performance_monitor = PerformanceMonitor()
        self.dataset_validator = DatasetValidator()
        logger.info("Initialized DeepfakeDetectionPipeline")
    
    def extract_features(self):
        """Extract features from all videos in dataset"""
        print("\n" + "="*60)
        print("🔧 FEATURE EXTRACTION")
        print("="*60)
        
        self.performance_monitor.start_timer("feature_extraction")
        
        # Validate dataset structure
        issues = self.dataset_validator.validate_dataset_structure(DATASET_PATH)
        if issues:
            print("❌ Dataset validation issues:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        
        total_processed = 0
        
        # Extract features from real videos
        real_path = DATASET_CONFIG["real_videos_path"]
        if os.path.exists(real_path):
            print(f"\n🎬 Processing REAL videos from: {real_path}")
            real_count = batch_extract_features(real_path, "features", "real")
            total_processed += real_count
            print(f"✅ Processed {real_count} real videos")
        
        # Extract features from fake videos
        fake_path = DATASET_CONFIG["fake_videos_path"]
        if os.path.exists(fake_path):
            print(f"\n🎭 Processing FAKE videos from: {fake_path}")
            fake_count = batch_extract_features(fake_path, "features", "fake")
            total_processed += fake_count
            print(f"✅ Processed {fake_count} fake videos")
        
        duration = self.performance_monitor.end_timer("feature_extraction")
        
        if total_processed > 0:
            print(f"\n🎉 Feature extraction completed!")
            print(f"📊 Total videos processed: {total_processed}")
            print(f"⏱️  Time taken: {duration:.2f} seconds")
            return True
        else:
            print("❌ No videos were processed")
            return False
    
    def train_models(self, model_type="auto"):
        """Train classification models"""
        print("\n" + "="*60)
        print("🤖 MODEL TRAINING")
        print("="*60)
        
        self.performance_monitor.start_timer("model_training")
        
        print(f"🎯 Training model type: {model_type}")
        
        success = train_model(model_type)
        
        duration = self.performance_monitor.end_timer("model_training")
        
        if success:
            print(f"\n🎉 Model training completed!")
            print(f"⏱️  Time taken: {duration:.2f} seconds")
            return True
        else:
            print("❌ Model training failed")
            return False
    
    def predict_videos(self, video_path=None, use_ensemble=False):
        """Make predictions on videos"""
        print("\n" + "="*60)
        print("🔮 VIDEO PREDICTION")
        print("="*60)
        
        self.performance_monitor.start_timer("prediction")
        
        try:
            if video_path and os.path.isfile(video_path):
                # Single video prediction
                print(f"🎬 Analyzing single video: {os.path.basename(video_path)}")
                result = predict_video(video_path, use_ensemble=use_ensemble)
                self._display_prediction_result(result, video_path)
                
            elif video_path and os.path.isdir(video_path):
                # Batch prediction
                print(f"📁 Analyzing videos in directory: {video_path}")
                results = predict_batch(video_path, use_ensemble=use_ensemble)
                self._display_batch_results(results)
                
            else:
                # Interactive mode
                self._interactive_prediction(use_ensemble)
            
            duration = self.performance_monitor.end_timer("prediction")
            print(f"\n⏱️  Prediction time: {duration:.2f} seconds")
            return True
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            print(f"❌ Prediction failed: {e}")
            return False
    
    def _display_prediction_result(self, result, video_path):
        """Display single prediction result"""
        if result["status"] == "success":
            prediction = result["prediction"].upper()
            confidence = result["confidence"]
            
            # Color coding for terminal
            if prediction == "REAL":
                color = "🟢"
            elif prediction == "FAKE":
                color = "🔴"
            else:
                color = "🟡"
            
            print(f"\n{color} RESULT: {prediction}")
            print(f"📊 Confidence: {confidence:.4f}")
            print(f"📈 Probabilities:")
            print(f"  Real: {result['probabilities']['real']:.4f}")
            print(f"  Fake: {result['probabilities']['fake']:.4f}")
            
            if "model_used" in result:
                print(f"🤖 Model: {result['model_used']}")
        else:
            print(f"❌ Prediction failed: {result.get('error', 'Unknown error')}")
    
    def _display_batch_results(self, results):
        """Display batch prediction results"""
        if not results:
            print("❌ No results to display")
            return
        
        print(f"\n📊 BATCH PREDICTION RESULTS")
        print("-" * 50)
        
        for result in results:
            filename = result["filename"]
            if result["status"] == "success":
                prediction = result["prediction"].upper()
                confidence = result["confidence"]
                
                if prediction == "REAL":
                    icon = "🟢"
                elif prediction == "FAKE":
                    icon = "🔴"
                else:
                    icon = "🟡"
                
                print(f"{icon} {filename}: {prediction} ({confidence:.3f})")
            else:
                print(f"❌ {filename}: FAILED")
        
        # Summary statistics
        successful = [r for r in results if r["status"] == "success"]
        if successful:
            real_count = len([r for r in successful if r["prediction"] == "real"])
            fake_count = len([r for r in successful if r["prediction"] == "fake"])
            uncertain_count = len([r for r in successful if r["prediction"] == "uncertain"])
            
            print(f"\n📈 SUMMARY:")
            print(f"  Total: {len(results)} videos")
            print(f"  Successful: {len(successful)}")
            print(f"  Real: {real_count}")
            print(f"  Fake: {fake_count}")
            print(f"  Uncertain: {uncertain_count}")
    
    def _interactive_prediction(self, use_ensemble=False):
        """Interactive prediction mode"""
        # Find available videos
        video_files = []
        
        # Search in dataset directories
        for root, dirs, files in os.walk(DATASET_PATH):
            for file in files:
                if any(file.lower().endswith(ext) for ext in DATASET_CONFIG["supported_formats"]):
                    video_files.append(os.path.join(root, file))
        
        if not video_files:
            print("❌ No videos found for prediction!")
            print("💡 Add videos to the dataset directory")
            return
        
        print(f"\n📹 Found {len(video_files)} videos:")
        for i, video_path in enumerate(video_files, 1):
            rel_path = os.path.relpath(video_path)
            print(f"  {i}. {rel_path}")
        
        try:
            choice = input(f"\nEnter video number (1-{len(video_files)}) or 'all' for batch: ").strip()
            
            if choice.lower() == 'all':
                # Batch prediction on all found videos
                for video_path in video_files:
                    print(f"\n--- {os.path.basename(video_path)} ---")
                    result = predict_video(video_path, use_ensemble=use_ensemble)
                    self._display_prediction_result(result, video_path)
            else:
                # Single video prediction
                video_num = int(choice)
                if 1 <= video_num <= len(video_files):
                    selected_video = video_files[video_num - 1]
                    result = predict_video(selected_video, use_ensemble=use_ensemble)
                    self._display_prediction_result(result, selected_video)
                else:
                    print("❌ Invalid video number!")
        
        except (ValueError, KeyboardInterrupt):
            print("\n👋 Prediction cancelled")
    
    def show_status(self):
        """Show current system status"""
        print("\n" + "="*60)
        print("📊 SYSTEM STATUS")
        print("="*60)
        
        # Dataset status
        real_path = DATASET_CONFIG["real_videos_path"]
        fake_path = DATASET_CONFIG["fake_videos_path"]
        
        real_count = len([f for f in os.listdir(real_path) if any(f.lower().endswith(ext) for ext in DATASET_CONFIG["supported_formats"])]) if os.path.exists(real_path) else 0
        fake_count = len([f for f in os.listdir(fake_path) if any(f.lower().endswith(ext) for ext in DATASET_CONFIG["supported_formats"])]) if os.path.exists(fake_path) else 0
        
        print(f"📹 Dataset:")
        print(f"  Real videos: {real_count}")
        print(f"  Fake videos: {fake_count}")
        print(f"  Total: {real_count + fake_count}")
        
        # Features status
        features_real = len([f for f in os.listdir("features") if f.endswith('.npy') and 'real' in f]) if os.path.exists("features") else 0
        features_fake = len([f for f in os.listdir("features") if f.endswith('.npy') and 'fake' in f]) if os.path.exists("features") else 0
        
        print(f"\n🔧 Features:")
        print(f"  Real features: {features_real}")
        print(f"  Fake features: {features_fake}")
        print(f"  Total: {features_real + features_fake}")
        
        # Models status
        models_available = []
        if os.path.exists("models"):
            for file in os.listdir("models"):
                if file.endswith('.pkl') and not file.endswith('_scaler.pkl') and not file.endswith('_pca.pkl'):
                    models_available.append(file.replace('.pkl', ''))
        
        print(f"\n🤖 Models:")
        if models_available:
            for model in models_available:
                print(f"  ✅ {model}")
        else:
            print("  ❌ No trained models found")
        
        # System capabilities
        print(f"\n⚙️  Capabilities:")
        for feature, available in AVAILABLE_FEATURES.items():
            status = "✅" if available else "❌"
            print(f"  {status} {feature.replace('_', ' ').title()}")

def show_menu():
    """Display main menu"""
    print("\n📋 DEEPFAKE DETECTION MENU")
    print("1. Extract Features")
    print("2. Train Model")
    print("3. Predict Single Video")
    print("4. Predict Folder")
    print("5. Launch GUI")
    print("0. Exit")
    print("Choose option:", end=" ")



def launch_gui():
    """Launch the GUI application"""
    try:
        from gui_app import DeepfakeDetectorGUI, GUI_AVAILABLE

        if not GUI_AVAILABLE:
            print("❌ GUI not available - tkinter not found")
            print("💡 Please install tkinter or use the command-line interface")
            return

        print("🚀 Launching GUI application...")
        app = DeepfakeDetectorGUI()
        app.run()

    except ImportError as e:
        print(f"❌ Failed to launch GUI: {e}")
        print("💡 Please ensure all GUI dependencies are installed")
    except Exception as e:
        print(f"❌ GUI error: {e}")

def main():
    """Main interactive interface"""
    print("🚀 Initializing Deepfake Detection System...")

    # Initialize system
    if not initialize_system():
        print("❌ System initialization failed!")
        return

    # Create pipeline
    pipeline = DeepfakeDetectionPipeline()
    
    while True:
        show_menu()
        
        try:
            choice = input().strip()

            if choice == '1':
                pipeline.extract_features()
            elif choice == '2':
                # Ask user which model type to train
                print("\nSelect model type:")
                print("1. Basic Model (Random Forest)")
                print("2. Advanced Model (Ensemble)")
                model_choice = input("Choose (1-2): ").strip()
                if model_choice == '1':
                    pipeline.train_models("random_forest")
                elif model_choice == '2':
                    pipeline.train_models("ensemble")
                else:
                    print("❌ Invalid choice!")
            elif choice == '3':
                video_path = input("Enter video path: ").strip()
                pipeline.predict_videos(video_path)
            elif choice == '4':
                dir_path = input("Enter directory path: ").strip()
                pipeline.predict_videos(dir_path)
            elif choice == '5':
                launch_gui()
            elif choice == '0':
                print("\n👋 Thank you for using Deepfake Detection System!")
                break
            else:
                print("❌ Invalid choice! Please enter a number between 0-5.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            print(f"❌ An error occurred: {e}")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()

# 🤖 Deepfake Detection System v3.0

A professional-grade deepfake detection system with ResNeXt-based feature extraction, ensemble learning, and multiple interfaces.

## 🚀 Features

- **ResNeXt-50 Feature Extraction** - State-of-the-art deep learning features
- **Multiple ML Algorithms** - Random Forest, SVM, XGBoost, Neural Networks
- **Ensemble Prediction** - Combines multiple models for better accuracy
- **Quality Enhancement** - Automatic video quality improvement
- **Batch Processing** - Analyze multiple videos efficiently
- **Multiple Interfaces** - Command line, GUI, and programmatic API
- **Graceful Degradation** - Works with minimal dependencies

## 📁 Project Structure

```
deepfake_detector/
├── main.py                 # Entry point: orchestrates full pipeline
├── config.py               # Centralized configuration
├── extractor.py            # Face detection & feature extraction (ResNeXt-based)
├── trainer.py              # Feature loading, training, and model saving
├── predictor.py            # Loads model, runs predictions on videos
├── utils.py                # Enhancement, normalization, smoothing etc.
├── gui_app.py              # Optional: GUI video upload and detection
├── requirements.txt        # Dependencies
├── README.md              # This file
├── features/               # Saved .npy feature files
├── models/                 # Saved classifier models (.pkl)
└── dataset/                # Real/Fake video dataset folder
    ├── real/
    └── fake/
```

## 🔧 Installation

### Quick Start (Basic Features)
```bash
pip install numpy scikit-learn joblib
```

### Full Installation (All Features)
```bash
pip install -r requirements.txt
```

### Manual Installation
```bash
# Core dependencies
pip install numpy scikit-learn joblib

# Computer vision
pip install opencv-python Pillow

# Deep learning
pip install torch torchvision facenet-pytorch

# Additional ML algorithms
pip install xgboost

# Visualization
pip install matplotlib seaborn
```

## 🎯 Quick Start

### 1. Prepare Dataset
```bash
# Add your videos to the dataset folders
deepfake_detector/dataset/real/     # Real videos
deepfake_detector/dataset/fake/     # Fake videos
```

### 2. Run the System
```bash
cd deepfake_detector
python main.py
```

### 3. Follow the Menu
1. Extract Features (Option 1)
2. Train Model (Option 2 or 3)
3. Make Predictions (Options 4-6)

## 💻 Usage Examples

### Command Line Interface
```bash
# Interactive menu
python main.py

# Direct training
python trainer.py

# Direct prediction
python predictor.py
```

### GUI Interface
```bash
python gui_app.py
```

### Programmatic API
```python
from config import initialize_system
from extractor import extract_features_from_video
from trainer import train_model
from predictor import predict_video

# Initialize system
initialize_system()

# Extract features
features = extract_features_from_video("video.mp4")

# Train model
train_model("ensemble")

# Make prediction
result = predict_video("test_video.mp4")
print(f"Prediction: {result['prediction']}")
print(f"Confidence: {result['confidence']}")
```

## 🔧 Configuration

All settings are centralized in `config.py`:

```python
# Feature extraction settings
EXTRACTION_CONFIG = {
    "feature_extractor": "resnext",  # "resnext", "resnet", "basic"
    "target_face_size": 224,
    "max_faces_per_video": 25,
    "enable_enhancement": True,
}

# Training settings
TRAINING_CONFIG = {
    "default_classifier": "random_forest",
    "enable_ensemble": True,
    "test_size": 0.2,
    "cross_validation_folds": 5,
}
```

## 🎯 Supported Video Formats

- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- FLV (.flv)
- WMV (.wmv)
- WebM (.webm)
- M4V (.m4v)

## 🤖 Available Models

### Individual Models
- **Random Forest** - Fast, reliable baseline
- **SVM** - Support Vector Machine with RBF kernel
- **XGBoost** - Gradient boosting (requires xgboost)
- **Neural Network** - Multi-layer perceptron

### Ensemble Models
- **Soft Voting** - Averages prediction probabilities
- **Hard Voting** - Majority vote from multiple models

## 📊 Performance

### Feature Extraction
- **Basic Mode**: ~1-2 seconds per video
- **Advanced Mode**: ~5-10 seconds per video (with face detection)

### Training
- **Small Dataset** (<100 videos): ~30 seconds
- **Medium Dataset** (100-1000 videos): ~2-5 minutes
- **Large Dataset** (>1000 videos): ~10+ minutes

### Prediction
- **Single Video**: ~2-5 seconds
- **Batch Processing**: ~1-2 seconds per video

## 🔍 Troubleshooting

### Common Issues

**"No trained model found"**
```bash
# Train a model first
python main.py
# Choose option 2 or 3
```

**"CUDA compatibility issues"**
- System automatically uses CPU for stability
- No action needed

**"No faces detected"**
- Check video quality
- Ensure faces are visible
- Try different videos

**"Import errors"**
```bash
# Install missing dependencies
pip install -r requirements.txt
```

### System Requirements

**Minimum:**
- Python 3.7+
- 4GB RAM
- 1GB disk space

**Recommended:**
- Python 3.8+
- 8GB RAM
- 5GB disk space
- GPU (optional, but faster)

## 🧪 Testing

```bash
# Test system initialization
python config.py

# Test feature extraction
python extractor.py

# Test training
python trainer.py

# Test prediction
python predictor.py
```

## 📈 Advanced Usage

### Custom Feature Extractors
```python
from extractor import BasicFeatureExtractor

class CustomExtractor(BasicFeatureExtractor):
    def extract_features_from_video(self, video_path):
        # Your custom extraction logic
        return features
```

### Custom Models
```python
from trainer import ModelTrainer

trainer = ModelTrainer()
# Add your custom model to the training pipeline
```

### Batch Processing
```python
from predictor import predict_batch

results = predict_batch("video_directory/", use_ensemble=True)
for result in results:
    print(f"{result['filename']}: {result['prediction']}")
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- **ResNeXt** - Facebook AI Research
- **MTCNN** - Joint Face Detection and Alignment
- **scikit-learn** - Machine Learning Library
- **OpenCV** - Computer Vision Library

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the configuration options
3. Test with the provided examples
4. Create an issue with detailed information

---

**Happy Deepfake Detection! 🕵️‍♂️🔍**
